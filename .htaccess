# BEGIN SpeedyCacheheaders
FileETag None
<IfModule mod_headers.c>
	Header unset ETag
</IfModule>
<FilesMatch "\.(html)$">
<IfModule mod_headers.c>
	Header set x-speedycache-source "Server"
	Header set Cache-Tag "test.supporttheravada.org"
	Header set CDN-Cache-Control "max-age=1296000"
	Header set Cache-Control "public"
	Header unset Pragma
	Header unset Last-Modified
</IfModule>
</FilesMatch>

<FilesMatch "\.(css|htc|js|asf|asx|wax|wmv|wmx|avi|bmp|class|divx|doc|docx|eot|exe|gif|gz|gzip|ico|jpg|jpeg|jpe|json|mdb|mid|midi|mov|qt|mp3|m4a|mp4|m4v|mpeg|mpg|mpe|mpp|otf|odb|odc|odf|odg|odp|ods|odt|ogg|pdf|png|pot|pps|ppt|pptx|ra|ram|svg|svgz|swf|tar|tif|tiff|ttf|ttc|wav|wma|wri|xla|xls|xlsx|xlt|xlw|zip)$">
	<IfModule mod_headers.c>
		Header unset Pragma
		Header set Cache-Control "public"
	</IfModule>
</FilesMatch>
# END SpeedyCacheheaders
# BEGIN Gzipspeedycache
<IfModule mod_deflate.c>
	AddOutputFilterByType DEFLATE font/opentype
	AddOutputFilterByType DEFLATE font/otf
	AddOutputFilterByType DEFLATE font/ttf
	AddOutputFilterByType DEFLATE font/woff
	AddOutputFilterByType DEFLATE font/woff2
	AddOutputFilterByType DEFLATE text/js
	AddOutputFilterByType DEFLATE text/css
	AddOutputFilterByType DEFLATE text/html
	AddOutputFilterByType DEFLATE text/javascript
	AddOutputFilterByType DEFLATE text/plain
	AddOutputFilterByType DEFLATE text/xml
	AddOutputFilterByType DEFLATE image/svg+xml
	AddOutputFilterByType DEFLATE image/x-icon
	AddOutputFilterByType DEFLATE application/javascript
	AddOutputFilterByType DEFLATE application/x-javascript
	AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
	AddOutputFilterByType DEFLATE application/x-font
	AddOutputFilterByType DEFLATE application/x-font-opentype
	AddOutputFilterByType DEFLATE application/x-font-otf
	AddOutputFilterByType DEFLATE application/x-font-truetype
	AddOutputFilterByType DEFLATE application/x-font-ttf
	AddOutputFilterByType DEFLATE application/font-woff2
	AddOutputFilterByType DEFLATE application/xhtml+xml
	AddOutputFilterByType DEFLATE application/xml
	AddOutputFilterByType DEFLATE application/rss+xml
</IfModule>
# END Gzipspeedycache
# BEGIN LBCspeedycache
<IfModule mod_expires.c>
	ExpiresActive on
	ExpiresDefault A0
	ExpiresByType text/css A31536000
	ExpiresByType text/javascript A31536000
	ExpiresByType font/ttf A31536000
	ExpiresByType font/otf A31536000
	ExpiresByType font/woff A31536000
	ExpiresByType font/woff2 A31536000
	ExpiresByType image/jpg A31536000
	ExpiresByType image/jpeg A31536000
	ExpiresByType image/png A31536000
	ExpiresByType image/gif A31536000
	ExpiresByType image/webp A31536000
	ExpiresByType image/x-icon A31536000
	ExpiresByType image/svg+xml A31536000
	ExpiresByType image/vnd.microsoft.icon A31536000
	ExpiresByType video/ogg A31536000
	ExpiresByType video/mp4 A31536000
	ExpiresByType video/webm A31536000
	ExpiresByType audio/ogg A31536000
	ExpiresByType application/pdf A31536000
	ExpiresByType application/javascript A31536000
	ExpiresByType application/x-javascript A31536000
	ExpiresByType application/x-font-ttf A31536000
	ExpiresByType application/x-font-woff A31536000
	ExpiresByType application/font-woff A31536000
	ExpiresByType application/font-woff2 A31536000
	ExpiresByType application/vnd.ms-fontobject A31536000
</IfModule>
# END LBCspeedycache
# BEGIN WEBPspeedycache
<IfModule mod_rewrite.c>
	RewriteEngine On
	RewriteCond %{HTTP_ACCEPT} image/webp
	RewriteCond %{REQUEST_FILENAME} \.(jpe?g|png|gif)$
	RewriteCond %{DOCUMENT_ROOT}/$1.webp -f
	RewriteRule ^(.+)\.(jpe?g|png|gif)$ $1.webp [T=image/webp,L]
</IfModule>
<IfModule mod_headers.c>
  Header append Vary Accept env=REDIRECT_accept
</IfModule>
AddType image/webp .webp
# END WEBPspeedycache
# BEGIN speedycache
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteBase /
	RewriteCond %{REQUEST_METHOD} GET
	RewriteCond %{HTTP_USER_AGENT} !(Mediatoolkitbot|facebookexternalhit|SpeedyCacheCCSS)
	RewriteCond %{QUERY_STRING} =""
	RewriteCond %{HTTP:Cookie} !(comment_author_|wordpress_logged_in_)

	RewriteCond %{REQUEST_URI} !(\/){2}$
	RewriteCond %{REQUEST_URI} !^/(wp-(?:admin|login|register|comments-post|cron|json))/ [NC]
	RewriteCond %{DOCUMENT_ROOT}/wp-content/cache/speedycache/%{HTTP_HOST}/all%{REQUEST_URI}/index.html -f
	RewriteRule ^(.*) /wp-content/cache/speedycache/%{HTTP_HOST}/all%{REQUEST_URI}/index.html [L]
</IfModule>
# END speedycache

# BEGIN WordPress
# The directives (lines) between "BEGIN WordPress" and "END WordPress" are
# dynamically generated, and should only be modified via WordPress filters.
# Any changes to the directives between these markers will be overwritten.
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
RewriteBase /
RewriteRule ^index\.php$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.php [L]
</IfModule>

# END WordPress

# BEGIN LiteSpeed
<IfModule Litespeed>
SetEnv noabort 1
</IfModule>
# END LiteSpeed
