<?php
session_start();
require_once '../config/database.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in and is super admin
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['is_super_admin']) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['user_id']) || !isset($input['new_role'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
    exit;
}

$userId = (int)$input['user_id'];
$newRole = $input['new_role'];

// Validate role
if (!in_array($newRole, ['donor', 'admin'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid role']);
    exit;
}

try {
    // Use direct PDO connection for better error handling
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get current user data
    $stmt = $pdo->prepare("SELECT id, email, role FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        throw new Exception('User not found');
    }

    $oldRole = $user['role'] ?? 'donor';

    // Update the user role
    $stmt = $pdo->prepare("UPDATE users SET role = ? WHERE id = ?");
    $stmt->execute([$newRole, $userId]);

    // If promoting to admin, create admin_users entry
    if ($newRole === 'admin' && $oldRole === 'donor') {
        // Check if admin_users entry already exists
        $stmt = $pdo->prepare("SELECT id FROM admin_users WHERE email = ?");
        $stmt->execute([$user['email']]);
        $existingAdmin = $stmt->fetch();

        if (!$existingAdmin) {
            // Create admin_users entry
            $stmt = $pdo->prepare("
                INSERT INTO admin_users (username, email, password_hash, role, created_at)
                VALUES (?, ?, ?, 'admin', NOW())
            ");
            $stmt->execute([
                $user['email'],
                $user['email'],
                password_hash('admin123', PASSWORD_DEFAULT)
            ]);
        }
    }

    // If demoting from admin, remove admin_users entry
    if ($newRole === 'donor' && $oldRole === 'admin') {
        $stmt = $pdo->prepare("DELETE FROM admin_users WHERE email = ? AND role = 'admin'");
        $stmt->execute([$user['email']]);
    }

    echo json_encode([
        'success' => true,
        'message' => 'User role updated successfully'
    ]);

} catch (Exception $e) {
    error_log("Role change error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Error updating user role: ' . $e->getMessage(),
        'debug' => [
            'user_id' => $userId,
            'new_role' => $newRole,
            'file' => __FILE__,
            'line' => $e->getLine()
        ]
    ]);
}
?>
