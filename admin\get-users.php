<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is super admin
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['is_super_admin']) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied']);
    exit;
}

$filter = $_GET['filter'] ?? 'all';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Build query based on filter
    $whereClause = '';
    $params = [];
    
    switch ($filter) {
        case 'donor':
            $whereClause = 'WHERE role = ?';
            $params[] = 'donor';
            break;
        case 'admin':
            $whereClause = 'WHERE role = ?';
            $params[] = 'admin';
            break;
        case 'all':
        default:
            // No filter
            break;
    }
    
    $query = "SELECT id, first_name, last_name, email, contact_number, role, is_active, created_at 
              FROM users 
              $whereClause 
              ORDER BY created_at DESC";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'users' => $users
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
