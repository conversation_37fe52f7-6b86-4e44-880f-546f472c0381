<?php
/**
 * Reset Super Admin Credentials
 * This script resets the super admin username and password
 * Username: <EMAIL>
 * Password: 123456
 */

require_once '../config/database.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Resetting Super Admin Credentials...</h2>";
    
    // New credentials
    $newUsername = '<EMAIL>';
    $newEmail = '<EMAIL>';
    $newPassword = '123456';
    
    // Hash the password
    $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
    
    echo "<p>New credentials:</p>";
    echo "<ul>";
    echo "<li><strong>Username:</strong> " . htmlspecialchars($newUsername) . "</li>";
    echo "<li><strong>Email:</strong> " . htmlspecialchars($newEmail) . "</li>";
    echo "<li><strong>Password:</strong> " . htmlspecialchars($newPassword) . "</li>";
    echo "</ul>";
    
    // Check if super admin exists
    $stmt = $pdo->prepare("SELECT id FROM admin_users WHERE role = 'super_admin' ORDER BY id ASC LIMIT 1");
    $stmt->execute();
    $superAdmin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($superAdmin) {
        // Update existing super admin
        echo "<p>Updating existing super admin (ID: {$superAdmin['id']})...</p>";
        
        $stmt = $pdo->prepare("
            UPDATE admin_users 
            SET username = ?, email = ?, password_hash = ?, is_active = 1 
            WHERE id = ?
        ");
        $stmt->execute([$newUsername, $newEmail, $passwordHash, $superAdmin['id']]);
        
        echo "<p style='color: green;'>✓ Super admin credentials updated successfully!</p>";
        
    } else {
        // Create new super admin
        echo "<p>Creating new super admin...</p>";
        
        $stmt = $pdo->prepare("
            INSERT INTO admin_users (username, email, password_hash, role, is_active, created_at) 
            VALUES (?, ?, ?, 'super_admin', 1, NOW())
        ");
        $stmt->execute([$newUsername, $newEmail, $passwordHash]);
        
        $newAdminId = $pdo->lastInsertId();
        echo "<p style='color: green;'>✓ New super admin created successfully! (ID: {$newAdminId})</p>";
    }
    
    // Verify the credentials work
    echo "<h3>Verifying credentials...</h3>";
    
    $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE username = ? AND role = 'super_admin'");
    $stmt->execute([$newUsername]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin && password_verify($newPassword, $admin['password_hash'])) {
        echo "<p style='color: green;'>✓ Credentials verified successfully!</p>";
        echo "<p><strong>Admin Details:</strong></p>";
        echo "<ul>";
        echo "<li>ID: " . $admin['id'] . "</li>";
        echo "<li>Username: " . htmlspecialchars($admin['username']) . "</li>";
        echo "<li>Email: " . htmlspecialchars($admin['email']) . "</li>";
        echo "<li>Role: " . htmlspecialchars($admin['role']) . "</li>";
        echo "<li>Active: " . ($admin['is_active'] ? 'Yes' : 'No') . "</li>";
        echo "<li>Created: " . $admin['created_at'] . "</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>✗ Error: Could not verify credentials!</p>";
    }
    
    // Show all admin users for reference
    echo "<h3>All Admin Users:</h3>";
    $stmt = $pdo->prepare("SELECT id, username, email, role, is_active, created_at FROM admin_users ORDER BY id");
    $stmt->execute();
    $allAdmins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($allAdmins) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Username</th>";
        echo "<th style='padding: 8px;'>Email</th>";
        echo "<th style='padding: 8px;'>Role</th>";
        echo "<th style='padding: 8px;'>Active</th>";
        echo "<th style='padding: 8px;'>Created</th>";
        echo "</tr>";
        
        foreach ($allAdmins as $admin) {
            $rowStyle = $admin['role'] === 'super_admin' ? 'background: #d4edda;' : '';
            echo "<tr style='{$rowStyle}'>";
            echo "<td style='padding: 8px;'>" . $admin['id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($admin['username']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($admin['email']) . "</td>";
            echo "<td style='padding: 8px;'>";
            if ($admin['role'] === 'super_admin') {
                echo "<strong style='color: #d4822a;'>SUPER ADMIN</strong>";
            } else {
                echo htmlspecialchars($admin['role']);
            }
            echo "</td>";
            echo "<td style='padding: 8px;'>" . ($admin['is_active'] ? '✓' : '✗') . "</td>";
            echo "<td style='padding: 8px;'>" . $admin['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<hr>";
    echo "<h3 style='color: green;'>✓ Super Admin Reset Complete!</h3>";
    echo "<p><strong>You can now login with:</strong></p>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #d4822a;'>";
    echo "<p><strong>Username:</strong> <EMAIL></p>";
    echo "<p><strong>Password:</strong> 123456</p>";
    echo "</div>";
    
    echo "<p style='margin-top: 20px;'>";
    echo "<a href='index.php' style='background: #d4822a; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Admin Login</a>";
    echo "</p>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin-top: 20px;'>";
    echo "<p><strong>⚠️ Security Note:</strong></p>";
    echo "<p>Please change the password after logging in for security purposes.</p>";
    echo "<p>You can change it in the admin settings page after logging in.</p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>Database Error:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}

h2, h3 {
    color: #333;
}

table {
    background: white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

ul {
    background: white;
    padding: 15px 30px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
</style>
