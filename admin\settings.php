<?php
/**
 * Admin Settings Page for Dhana Reservation System
 */

session_start();
require_once '../config/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: index.php');
    exit;
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit;
}

$db = getDB();
$successMessage = '';
$errorMessage = '';

// Get current admin details
$adminId = $_SESSION['admin_id'];
$admin = $db->fetchOne(
    "SELECT * FROM admin_users WHERE id = ?",
    [$adminId]
);

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_profile'])) {
        $username = trim($_POST['username']);
        $email = trim($_POST['email']);
        $phone = trim($_POST['phone']);
        
        // Validation
        if (empty($username) || empty($email)) {
            $errorMessage = 'Username and email are required.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errorMessage = 'Please enter a valid email address.';
        } else {
            try {
                // Check if username/email already exists for other users
                $existingUser = $db->fetchOne(
                    "SELECT id FROM admin_users WHERE (username = ? OR email = ?) AND id != ?",
                    [$username, $email, $adminId]
                );
                
                if ($existingUser) {
                    $errorMessage = 'Username or email already exists.';
                } else {
                    // Update profile
                    $db->query(
                        "UPDATE admin_users SET username = ?, email = ?, phone = ? WHERE id = ?",
                        [$username, $email, $phone, $adminId]
                    );
                    
                    // Update session
                    $_SESSION['admin_username'] = $username;
                    
                    $successMessage = 'Profile updated successfully!';
                    
                    // Refresh admin data
                    $admin = $db->fetchOne(
                        "SELECT * FROM admin_users WHERE id = ?",
                        [$adminId]
                    );
                }
            } catch (Exception $e) {
                $errorMessage = 'Error updating profile: ' . $e->getMessage();
            }
        }
    }
    
    if (isset($_POST['change_password'])) {
        $currentPassword = $_POST['current_password'];
        $newPassword = $_POST['new_password'];
        $confirmPassword = $_POST['confirm_password'];
        
        // Validation
        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            $errorMessage = 'All password fields are required.';
        } elseif (!password_verify($currentPassword, $admin['password_hash'])) {
            $errorMessage = 'Current password is incorrect.';
        } elseif ($newPassword !== $confirmPassword) {
            $errorMessage = 'New passwords do not match.';
        } elseif (strlen($newPassword) < 6) {
            $errorMessage = 'New password must be at least 6 characters long.';
        } else {
            try {
                $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
                $db->query(
                    "UPDATE admin_users SET password_hash = ? WHERE id = ?",
                    [$newPasswordHash, $adminId]
                );
                
                $successMessage = 'Password changed successfully!';
            } catch (Exception $e) {
                $errorMessage = 'Error changing password: ' . $e->getMessage();
            }
        }
    }
}

// Check if phone column exists, if not add it
try {
    $columns = $db->fetchAll("SHOW COLUMNS FROM admin_users LIKE 'phone'");
    if (empty($columns)) {
        $db->query("ALTER TABLE admin_users ADD COLUMN phone VARCHAR(20) NULL AFTER email");
    }
} catch (Exception $e) {
    // Column might already exist or there's a permission issue
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Settings - Dhana Booking System</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-panel">
        <!-- Admin Header -->
        <div class="admin-header">
            <div class="admin-nav">
                <h1><i class="fas fa-cog"></i> Admin Settings</h1>
                <div class="admin-user">
                    <span>Welcome, <?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                    <a href="index.php" class="back-btn">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    <a href="?logout=1" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content Layout -->
        <div class="admin-layout">
            <div class="admin-main-content">
                <div class="admin-section">
                <?php if ($successMessage): ?>
                    <div class="success-message">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars($successMessage); ?>
                    </div>
                <?php endif; ?>

                <?php if ($errorMessage): ?>
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo htmlspecialchars($errorMessage); ?>
                    </div>
                <?php endif; ?>

                <div class="settings-container">
                    <!-- Left Column: Account Information -->
                    <div class="settings-section">
                        <h2><i class="fas fa-info-circle"></i> Account Information</h2>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Account Created</label>
                                <span><?php echo date('F j, Y', strtotime($admin['created_at'])); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Role</label>
                                <span><?php echo ucfirst($admin['role']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Status</label>
                                <span class="status-active">
                                    <i class="fas fa-check-circle"></i> Active
                                </span>
                            </div>
                            <div class="info-item">
                                <label>Last Login</label>
                                <span><?php echo date('F j, Y g:i A'); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Middle Column: Profile Settings -->
                    <div class="settings-section">
                        <h2><i class="fas fa-user"></i> Profile Settings</h2>
                        <form method="POST" class="settings-form">
                            <div class="form-group">
                                <label for="username">Username</label>
                                <input type="text" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($admin['username']); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($admin['email']); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($admin['phone'] ?? ''); ?>" 
                                       placeholder="Enter phone number">
                            </div>

                            <button type="submit" name="update_profile" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Profile
                            </button>
                        </form>
                    </div>

                    <!-- Right Column: Password Change -->
                    <div class="settings-section">
                        <h2><i class="fas fa-lock"></i> Change Password</h2>
                        <form method="POST" class="settings-form">
                            <div class="form-group">
                                <label for="current_password">Current Password</label>
                                <input type="password" id="current_password" name="current_password" required>
                            </div>

                            <div class="form-group">
                                <label for="new_password">New Password</label>
                                <input type="password" id="new_password" name="new_password" 
                                       minlength="6" required>
                                <small>Minimum 6 characters</small>
                            </div>

                            <div class="form-group">
                                <label for="confirm_password">Confirm New Password</label>
                                <input type="password" id="confirm_password" name="confirm_password" 
                                       minlength="6" required>
                            </div>

                            <button type="submit" name="change_password" class="btn btn-secondary">
                                <i class="fas fa-key"></i> Change Password
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script>
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });

        // Auto-hide messages after 5 seconds
        setTimeout(function() {
            const messages = document.querySelectorAll('.success-message, .error-message');
            messages.forEach(function(message) {
                message.style.opacity = '0';
                setTimeout(function() {
                    message.style.display = 'none';
                }, 300);
            });
        }, 5000);
    </script>
</body>
</html>
