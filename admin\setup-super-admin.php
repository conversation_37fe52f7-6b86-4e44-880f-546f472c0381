<?php
/**
 * Super Admin Setup Script
 * Run this once to set up super admin functionality
 */

require_once '../config/database.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Setting up Super Admin functionality...</h2>";
    
    // Create admin_actions table
    echo "<p>Creating admin_actions table...</p>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_actions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT NOT NULL,
            action_type ENUM('booking_update', 'user_status_change', 'settings_update', 'dhana_type_update') NOT NULL,
            action_description TEXT NOT NULL,
            target_id INT NULL,
            old_values JSON NULL,
            new_values JSON NULL,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            approved_by INT NULL,
            approved_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (admin_id) REFERENCES admin_users(id),
            FOREIGN KEY (approved_by) REFERENCES admin_users(id)
        )
    ");
    
    // Add role column to users table if it doesn't exist
    echo "<p>Adding role column to users table...</p>";
    try {
        $pdo->exec("ALTER TABLE users ADD COLUMN role ENUM('donor', 'admin') DEFAULT 'donor' AFTER is_active");
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') === false) {
            throw $e;
        }
        echo "<p>Role column already exists.</p>";
    }
    
    // Add admin_user_id column to users table if it doesn't exist
    echo "<p>Adding admin_user_id column to users table...</p>";
    try {
        $pdo->exec("ALTER TABLE users ADD COLUMN admin_user_id INT NULL AFTER role");
        $pdo->exec("ALTER TABLE users ADD FOREIGN KEY (admin_user_id) REFERENCES admin_users(id)");
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') === false) {
            throw $e;
        }
        echo "<p>Admin_user_id column already exists.</p>";
    }
    
    // Create user_role_changes table
    echo "<p>Creating user_role_changes table...</p>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS user_role_changes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            old_role ENUM('donor', 'admin') NOT NULL,
            new_role ENUM('donor', 'admin') NOT NULL,
            changed_by INT NOT NULL,
            reason TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (changed_by) REFERENCES admin_users(id)
        )
    ");
    
    // Update first admin to super_admin
    echo "<p>Setting first admin as super admin...</p>";
    $pdo->exec("UPDATE admin_users SET role = 'super_admin' WHERE id = 1");
    
    // Create view for pending actions
    echo "<p>Creating pending_admin_actions view...</p>";
    $pdo->exec("
        CREATE OR REPLACE VIEW pending_admin_actions AS
        SELECT 
            aa.*,
            au.username as admin_username,
            au.email as admin_email,
            sau.username as approver_username
        FROM admin_actions aa
        JOIN admin_users au ON aa.admin_id = au.id
        LEFT JOIN admin_users sau ON aa.approved_by = sau.id
        WHERE aa.status = 'pending'
        ORDER BY aa.created_at ASC
    ");
    
    echo "<h3 style='color: green;'>✓ Super Admin setup completed successfully!</h3>";
    echo "<p><strong>Important:</strong></p>";
    echo "<ul>";
    echo "<li>The first admin user (ID: 1) has been set as Super Admin</li>";
    echo "<li>Regular admins will now need approval for booking status changes</li>";
    echo "<li>Super admins can manage user roles and approve admin actions</li>";
    echo "<li>Users can be promoted to admin status through the User Management interface</li>";
    echo "</ul>";
    echo "<p><a href='index.php'>← Back to Admin Panel</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>Error setting up Super Admin functionality:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
