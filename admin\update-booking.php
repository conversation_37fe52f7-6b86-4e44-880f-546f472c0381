<?php
/**
 * Update Reservation API
 * Updates reservation information from admin panel
 */

session_start();
require_once '../config/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

header('Content-Type: application/json');

try {
    $db = getDB();
    
    // Validate required fields
    $bookingId = isset($_POST['booking_id']) ? (int)$_POST['booking_id'] : 0;
    $bookingDate = isset($_POST['booking_date']) ? $_POST['booking_date'] : '';
    $dhanaTypeId = isset($_POST['dhana_type_id']) ? (int)$_POST['dhana_type_id'] : 0;
    $bookingTimeSlot = isset($_POST['booking_time_slot']) ? $_POST['booking_time_slot'] : '';
    $status = isset($_POST['status']) ? $_POST['status'] : '';
    $totalAmount = isset($_POST['total_amount']) ? (float)$_POST['total_amount'] : 0;
    $specialRequests = isset($_POST['special_requests']) ? trim($_POST['special_requests']) : '';
    $travelSupport = isset($_POST['travel_support']) ? 1 : 0;
    $isMonk = isset($_POST['is_monk']) ? 1 : 0;
    
    // Validation
    if (!$bookingId) {
        throw new Exception('Invalid reservation ID');
    }

    if (!$bookingDate || !strtotime($bookingDate)) {
        throw new Exception('Invalid reservation date');
    }
    
    if (!$dhanaTypeId) {
        throw new Exception('Please select a dhana type');
    }
    
    if (!in_array($bookingTimeSlot, ['morning', 'lunch', 'whole_day'])) {
        throw new Exception('Invalid time slot');
    }
    
    if (!in_array($status, ['pending', 'payment_pending', 'confirmed', 'completed', 'cancelled'])) {
        throw new Exception('Invalid status');
    }
    
    if ($totalAmount < 0) {
        throw new Exception('Total amount cannot be negative');
    }
    
    // Check if reservation exists
    $existingBooking = $db->fetchOne(
        "SELECT id, booking_date, dhana_type_id, booking_time_slot FROM bookings WHERE id = ?",
        [$bookingId]
    );
    
    if (!$existingBooking) {
        throw new Exception('Booking not found');
    }
    
    // Check for conflicts if date, dhana type, or time slot changed
    if ($existingBooking['booking_date'] !== $bookingDate || 
        $existingBooking['dhana_type_id'] != $dhanaTypeId || 
        $existingBooking['booking_time_slot'] !== $bookingTimeSlot) {
        
        $conflictCheck = $db->fetchOne(
            "SELECT id FROM bookings 
             WHERE booking_date = ? AND dhana_type_id = ? AND booking_time_slot = ? 
             AND id != ? AND status NOT IN ('cancelled')",
            [$bookingDate, $dhanaTypeId, $bookingTimeSlot, $bookingId]
        );
        
        if ($conflictCheck) {
            throw new Exception('Another booking already exists for this date, dhana type, and time slot');
        }
    }
    
    // Verify dhana type exists and is active
    $dhanaType = $db->fetchOne(
        "SELECT id, name, price FROM dhana_types WHERE id = ? AND is_active = 1",
        [$dhanaTypeId]
    );
    
    if (!$dhanaType) {
        throw new Exception('Invalid or inactive dhana type');
    }
    
    // Start transaction
    $db->getConnection()->beginTransaction();
    
    try {
        // Update reservation
        $db->query(
            "UPDATE bookings SET 
                booking_date = ?, 
                dhana_type_id = ?, 
                booking_time_slot = ?, 
                status = ?, 
                total_amount = ?, 
                special_requests = ?, 
                travel_support = ?, 
                is_monk = ?,
                updated_at = CURRENT_TIMESTAMP
             WHERE id = ?",
            [
                $bookingDate, 
                $dhanaTypeId, 
                $bookingTimeSlot, 
                $status, 
                $totalAmount, 
                $specialRequests, 
                $travelSupport, 
                $isMonk, 
                $bookingId
            ]
        );
        
        // Log the change (optional - you could create an admin_logs table)
        // For now, we'll just commit the transaction
        
        $db->getConnection()->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Reservation updated successfully'
        ]);
        
    } catch (Exception $e) {
        $db->getConnection()->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
