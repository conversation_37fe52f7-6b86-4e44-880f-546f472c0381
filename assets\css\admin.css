/* Admin Panel Styles for Dhana Booking System */

.admin-panel {
    min-height: 100vh;
    background: #f5f5f5;
    display: flex;
    flex-direction: column;
}

/* Admin Header */
.admin-header {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
    padding: 20px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.admin-nav {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-nav h1 {
    margin: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.admin-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

.admin-user span {
    font-size: 0.9rem;
    opacity: 0.9;
}

.settings-btn,
.logout-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 15px;
    border-radius: 5px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.settings-btn:hover,
.logout-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

/* Main Layout */
.admin-layout {
    display: flex;
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    gap: 0;
    min-height: calc(100vh - 80px);
}

/* Left Sidebar */
.admin-sidebar {
    width: 320px;
    background: white;
    border-right: 1px solid #e9ecef;
    padding: 30px 20px;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
}

.sidebar-section h3 {
    color: #495057;
    font-size: 1.1rem;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

/* Main Content Area */
.admin-main-content {
    flex: 1;
    padding: 30px;
    background: #f5f5f5;
    overflow-y: auto;
}

/* Sidebar Stat Cards */
.stat-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    cursor: pointer;
    user-select: none;
}

.stat-card:hover {
    background: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateX(5px);
}

.stat-card.active {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
    border-color: transparent;
    box-shadow: 0 6px 20px rgba(212, 130, 42, 0.3);
    transform: translateX(5px);
}

.stat-card.active:hover {
    background: linear-gradient(135deg, #b8860b 0%, #a0522d 100%);
    transform: translateX(8px);
}

.stat-card.active .stat-info h4,
.stat-card.active .stat-info p {
    color: white;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: white;
    flex-shrink: 0;
}

.stat-icon.total {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
}

.stat-icon.pending {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
}

.stat-icon.payment {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.stat-icon.confirmed {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stat-icon.revenue {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.stat-icon.analytics {
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
}

.analytics-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.analytics-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(111, 66, 193, 0.2);
}

/* Analytics View Container */
.analytics-view-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    margin-top: 20px;
}

.analytics-view-container .analytics-header {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
    padding: 20px 25px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 20px;
}

.analytics-view-container .analytics-title h2 {
    margin: 0 0 5px 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.analytics-view-container .analytics-title p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.analytics-view-container .analytics-content {
    padding: 25px;
}

/* Loading and Error States */
.loading-state, .error-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.loading-state i, .error-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.loading-state i {
    color: #d4822a;
}

.error-state i {
    color: #dc3545;
}

.retry-btn {
    background: #d4822a;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    margin-top: 15px;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    background: #b8860b;
    transform: translateY(-2px);
}

/* Donors List */
.donors-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 400px;
    overflow-y: auto;
}

.donor-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.donor-item:hover {
    background: linear-gradient(135deg, #fef9f3 0%, #fdf6ed 100%);
    transform: translateX(5px);
}

.donor-rank {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.8rem;
    flex-shrink: 0;
}

.donor-info {
    flex: 1;
}

.donor-info strong {
    display: block;
    color: #333;
    margin-bottom: 2px;
}

.donor-info small {
    color: #666;
    font-size: 0.8rem;
}

.donor-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.donor-count {
    background: #17a2b8;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
}

.donor-amount {
    background: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
}

/* Responsive adjustments for analytics */
@media (max-width: 768px) {
    .analytics-view-container .analytics-header {
        flex-direction: column;
        align-items: stretch;
    }

    .analytics-view-container .analytics-content {
        padding: 20px;
    }

    .donor-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .donor-stats {
        align-items: flex-start;
        flex-direction: row;
        gap: 8px;
    }
}

.stat-info {
    flex: 1;
}

.stat-info h4 {
    margin: 0 0 5px 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
}

.stat-info p {
    margin: 0;
    color: #666;
    font-size: 0.85rem;
    font-weight: 500;
    line-height: 1.2;
}

.stat-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* Admin Sections */
.admin-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
    min-height: 50px;
}

.section-header h2 {
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    line-height: 1.2;
    height: 42px;
}

.section-header h2 i {
    color: #d4822a;
}

.section-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: flex-end;
    height: 42px;
    flex-shrink: 0;
}

.section-actions .btn {
    /* Reset all properties to override global styles */
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    background: none !important;
    font: inherit !important;
    color: inherit !important;
    text-decoration: none !important;
    outline: none !important;
    width: auto !important;
    text-align: center !important;
    transform: none !important;

    /* Apply consistent styling */
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    padding: 11px 20px !important;
    height: 42px !important;
    border-radius: 8px !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-sizing: border-box !important;
    vertical-align: top !important;
    line-height: 1 !important;
    position: relative !important;
    top: 0 !important;
}

.section-actions .btn-primary {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%) !important;
    color: white !important;
    border: 1px solid transparent !important;
}

.section-actions .btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(212, 130, 42, 0.4) !important;
}

.section-actions .btn-secondary {
    background: #f8f9fa !important;
    color: #495057 !important;
    border: 1px solid #dee2e6 !important;
}

.section-actions .btn-secondary:hover {
    background: #e9ecef !important;
    border-color: #adb5bd !important;
    transform: translateY(-2px) !important;
}

.section-actions .btn-info {
    background: #8b4513 !important;
    color: white !important;
    border: 1px solid transparent !important;
}

.section-actions .btn-info:hover {
    background: #a0522d !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(139, 69, 19, 0.4) !important;
}

.section-actions .btn-warning {
    background: #ffc107 !important;
    color: #212529 !important;
    border: 1px solid transparent !important;
}

.section-actions .btn-warning:hover {
    background: #e0a800 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4) !important;
}

/* Search Container */
.search-container {
    margin-bottom: 25px;
}

.search-field {
    position: relative;
    max-width: 400px;
}

.search-field i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 1rem;
}

.search-field input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.search-field input:focus {
    outline: none;
    border-color: #d4822a;
    background: white;
    box-shadow: 0 0 0 3px rgba(212, 130, 42, 0.1);
}

/* Bookings Table */
.bookings-table-container {
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.bookings-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.bookings-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 18px 15px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bookings-table td {
    padding: 18px 15px;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
    font-size: 0.95rem;
}

.bookings-table tr:hover {
    background: #f8f9fa;
}

.donor-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.donor-info strong {
    color: #333;
    font-size: 0.9rem;
}

.donor-info small {
    color: #666;
    font-size: 0.8rem;
}

.receipt-link {
    color: #d4822a;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
}

.receipt-link:hover {
    text-decoration: underline;
}

.no-receipt {
    color: #999;
    font-style: italic;
    font-size: 0.9rem;
}

/* Status Form */
.status-form {
    margin: 0;
}

.status-form select {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.8rem;
    background: white;
    cursor: pointer;
}

.status-form select:focus {
    outline: none;
    border-color: #d4822a;
}

/* Status Badges (reuse from main styles) */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    display: inline-block;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-payment_pending {
    background: #f8d7da;
    color: #721c24;
}

.status-confirmed {
    background: #d4edda;
    color: #155724;
}

.status-completed {
    background: #d1ecf1;
    color: #0c5460;
}

.status-cancelled {
    background: #f5c6cb;
    color: #721c24;
}

/* Success Message */
.success-message {
    background: #d4edda;
    color: #155724;
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #28a745;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Filter Indicator Styles */
.filter-indicator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.filter-label {
    color: #1976d2;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-label i {
    color: #1976d2;
}

.clear-filter {
    color: #d32f2f;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.clear-filter:hover {
    background-color: #ffebee;
}

/* Pagination Styles */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.pagination-info {
    color: #6c757d;
    font-size: 0.9rem;
}

.pagination-controls {
    display: flex;
    gap: 5px;
}

.pagination-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
    padding: 0 10px;
    border-radius: 4px;
    background-color: #f8f9fa;
    color: #495057;
    text-decoration: none;
    font-size: 0.9rem;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
}

.pagination-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.pagination-link.active {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
    border-color: transparent;
    font-weight: 500;
}

.first-page, .prev-page, .next-page, .last-page {
    font-size: 0.8rem;
}

/* Admin Calendar Styles */
.admin-calendar-container {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.calendar-nav {
    display: flex;
    align-items: center;
    gap: 20px;
}

.calendar-nav h3 {
    margin: 0;
    font-size: 1.5rem;
    color: #333;
    min-width: 200px;
    text-align: center;
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.calendar-nav h3:hover {
    background: #f8f9fa;
    color: #d4822a;
}

.calendar-nav .nav-btn {
    background: #d4822a;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.calendar-nav .nav-btn:hover {
    background: #b8860b;
    transform: scale(1.1);
}

.calendar-legend {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    border: 1px solid #ddd;
}

.legend-color.available { background: #d4edda; }
.legend-color.partially-booked { background: #fff3cd; }
.legend-color.fully-booked { background: #f8d7da; }
.legend-color.annual-event { background: #e3f2fd; }

.admin-calendar-grid .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.admin-calendar-grid .day-header {
    background: #d4822a;
    color: white;
    padding: 15px 10px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.admin-calendar-grid .day-cell {
    background: white;
    min-height: 100px;
    padding: 8px;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
}

.admin-calendar-grid .day-cell:hover {
    background: #f8f9fa;
    transform: scale(1.02);
}

.admin-calendar-grid .day-cell.empty {
    background: #f8f9fa;
    cursor: default;
}

.admin-calendar-grid .day-cell.empty:hover {
    transform: none;
}

.admin-calendar-grid .day-number {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 5px;
}

.admin-calendar-grid .day-cell.today .day-number {
    color: #d4822a;
    background: #fdf5e6;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.admin-calendar-grid .day-cell.past {
    opacity: 0.6;
    background: #f8f9fa;
}

.admin-calendar-grid .day-cell.blocked {
    background: #ffebee;
    color: #d32f2f;
}

.admin-calendar-grid .day-cell.available {
    border-left: 4px solid #28a745;
}

.admin-calendar-grid .day-cell.partially-booked {
    border-left: 4px solid #ffc107;
}

.admin-calendar-grid .day-cell.fully-booked {
    border-left: 4px solid #dc3545;
}

.booking-indicators {
    display: flex;
    flex-wrap: wrap;
    gap: 3px;
    margin-top: auto;
}

.booking-indicator {
    font-size: 8px;
    color: #d4822a;
    opacity: 0.8;
}

.booking-indicator.annual {
    color: #8b4513;
}

.calendar-loading,
.calendar-error {
    text-align: center;
    padding: 40px;
    color: #666;
    font-size: 1.1rem;
}

.calendar-error {
    color: #dc3545;
}

.calendar-booking-details {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.calendar-booking-details h4 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.3rem;
}

.booking-details-list {
    display: grid;
    gap: 15px;
}

.booking-detail-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.booking-detail-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.booking-detail-card.annual-booking {
    border-left: 4px solid #2196f3;
    background: #f3f8ff;
}

.booking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.booking-header h5 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
}

.booking-info p {
    margin: 8px 0;
    font-size: 0.9rem;
    color: #555;
}

.booking-info strong {
    color: #333;
}

.annual-badge {
    color: white !important;
    font-weight: 600;
}

.booking-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
}

.no-bookings {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 20px;
}

/* Month/Year Picker Modal */
.month-year-picker-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.month-year-picker-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

.picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
}

.picker-header h4 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
}

.close-picker {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-picker:hover {
    background: #f8f9fa;
    color: #333;
}

.picker-body {
    padding: 25px;
}

.year-selector {
    margin-bottom: 25px;
}

.year-selector label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.year-selector select {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
}

.year-selector select:focus {
    outline: none;
    border-color: #d4822a;
}

.month-selector label {
    display: block;
    margin-bottom: 15px;
    font-weight: 600;
    color: #333;
}

.month-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
}

.month-btn {
    padding: 12px 8px;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    color: #333;
}

.month-btn:hover {
    border-color: #d4822a;
    background: #fdf5e6;
}

.month-btn.selected {
    background: #d4822a;
    border-color: #d4822a;
    color: white;
}

.picker-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
}

.picker-footer .btn {
    /* Equal button sizing using flexbox */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 180px !important;
    height: 45px !important;
    min-width: 180px !important;
    max-width: 180px !important;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    flex: none !important;
    text-align: center !important;
}

.picker-footer .btn-secondary {
    background: #f8f9fa !important;
    color: #495057 !important;
    border: 1px solid #dee2e6 !important;
}

.picker-footer .btn-secondary:hover {
    background: #e9ecef !important;
    border-color: #adb5bd !important;
    transform: translateY(-1px) !important;
}

.picker-footer .btn-primary {
    background: #d4822a !important;
    color: white !important;
    border: none !important;
}

.picker-footer .btn-primary:hover {
    background: #b8860b !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(212, 130, 42, 0.3) !important;
}

/* Edit Booking Modal */
.edit-booking-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    overflow-y: auto;
}

.edit-booking-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    flex-shrink: 0;
}

.modal-header h4 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-modal:hover {
    background: #f8f9fa;
    color: #333;
}

.modal-body {
    padding: 25px;
    overflow-y: auto;
    flex: 1;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
    background: white;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #d4822a;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

.form-group label:has(input[type="checkbox"]) {
    display: flex;
    align-items: center;
    font-weight: 500;
    cursor: pointer;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    flex-shrink: 0;
}

.modal-footer .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.modal-footer .btn-secondary {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
}

.modal-footer .btn-secondary:hover {
    background: #e9ecef;
}

.modal-footer .btn-primary {
    background: #d4822a;
    color: white;
}

.modal-footer .btn-primary:hover {
    background: #b8860b;
}

.modal-footer .btn-danger {
    background: #dc3545;
    color: white;
}

.modal-footer .btn-danger:hover {
    background: #c82333;
}

.modal-footer .btn-success {
    background: #28a745;
    color: white;
}

.modal-footer .btn-success:hover {
    background: #218838;
}

/* Report Generator Modal */
.report-generator-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    overflow-y: auto;
}

.report-generator-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
    display: flex;
    flex-direction: column;
}

.report-type-section {
    margin-bottom: 30px;
}

.report-type-section h5 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid #d4822a;
    padding-bottom: 8px;
}

.report-type-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.report-type-option {
    cursor: pointer;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0;
    transition: all 0.3s ease;
    overflow: hidden;
}

.report-type-option:hover {
    border-color: #d4822a;
    box-shadow: 0 4px 12px rgba(212, 130, 42, 0.15);
}

.report-type-option input[type="radio"] {
    display: none;
}

.report-type-option input[type="radio"]:checked + .option-content {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
}

.option-content {
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.option-content i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
    color: #d4822a;
}

.report-type-option input[type="radio"]:checked + .option-content i {
    color: white;
}

.option-content span {
    display: block;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 5px;
}

.option-content small {
    display: block;
    opacity: 0.8;
    font-size: 0.85rem;
}

.date-selection-section {
    margin-bottom: 30px;
}

.date-option {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.report-options-section h5 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid #d4822a;
    padding-bottom: 8px;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    font-weight: 500;
    cursor: pointer;
    padding: 8px 0;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 10px;
    width: 18px;
    height: 18px;
    accent-color: #d4822a;
}

/* Modal Animation */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-nav {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .admin-nav h1 {
        font-size: 1.3rem;
    }
    
    .dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin: 20px auto;
    }
    
    .stat-card {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }
    
    .stat-info h3 {
        font-size: 1.5rem;
    }
    
    .admin-section {
        margin: 20px auto;
        padding: 0 10px;
    }
    
    .bookings-table-container {
        overflow-x: auto;
    }
    
    .bookings-table {
        min-width: 800px;
    }
    
    .bookings-table th,
    .bookings-table td {
        padding: 10px 8px;
        font-size: 0.8rem;
    }

    .pagination {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .pagination-controls {
        justify-content: center;
    }

    .pagination-link {
        min-width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    /* Calendar responsive styles */
    .calendar-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .calendar-legend {
        justify-content: center;
        gap: 15px;
    }

    .admin-calendar-grid .day-cell {
        min-height: 80px;
        padding: 5px;
    }

    .admin-calendar-grid .day-header {
        padding: 10px 5px;
        font-size: 0.8rem;
    }

    .booking-detail-card {
        padding: 15px;
    }

    .booking-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .booking-actions {
        flex-direction: column;
    }

    /* Modal responsive styles */
    .month-year-picker-content,
    .edit-booking-content {
        width: 95%;
        margin: 20px;
    }

    .picker-header,
    .modal-header {
        padding: 15px 20px;
    }

    .picker-body,
    .modal-body {
        padding: 20px;
    }

    .picker-footer,
    .modal-footer {
        padding: 15px 20px;
        flex-direction: column;
        gap: 10px;
    }

    .month-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    /* Report generator responsive styles */
    .report-generator-content {
        width: 95%;
        margin: 10px;
    }

    .report-type-options {
        grid-template-columns: 1fr;
    }

    .option-content {
        padding: 15px;
    }

    .option-content i {
        font-size: 1.5rem;
    }

    .checkbox-group {
        gap: 8px;
    }
}

@media (max-width: 480px) {
    .admin-header {
        padding: 15px 0;
    }
    
    .admin-nav {
        padding: 0 15px;
    }
    
    .admin-nav h1 {
        font-size: 1.2rem;
    }
    
    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: 10px;
        margin: 15px auto;
    }
    
    .stat-card {
        padding: 15px;
    }
    
    .admin-section {
        padding: 0 15px;
    }
    
    .admin-section h2 {
        font-size: 1.3rem;
    }
    
    .bookings-table th,
    .bookings-table td {
        padding: 8px 6px;
        font-size: 0.75rem;
    }
    
    .donor-info strong {
        font-size: 0.8rem;
    }

    .donor-info small {
        font-size: 0.7rem;
    }
    
    .status-form select {
        font-size: 0.7rem;
        padding: 4px 6px;
    }
}

/* Loading animation for status updates */
.status-form.loading select {
    opacity: 0.6;
    pointer-events: none;
}

/* Super Admin Styles */
.super-admin-badge {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    margin-left: 8px;
    text-transform: uppercase;
}

.user-management-btn {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
    color: #212529;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    text-decoration: none;
    font-size: 0.9rem;
    margin-left: 10px;
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
    vertical-align: middle;
    line-height: 1.5;
    box-sizing: border-box;
}

.user-management-btn:hover {
    background: linear-gradient(135deg, #e0a800 0%, #e68900 100%);
    transform: translateY(-1px);
}

.approvals-btn {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
    color: #212529;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    text-decoration: none;
    font-size: 0.9rem;
    margin-left: 10px;
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
    vertical-align: middle;
    line-height: 1.5;
    box-sizing: border-box;
}

.approvals-btn:hover {
    background: linear-gradient(135deg, #e0a800 0%, #e68900 100%);
    transform: translateY(-1px);
}

.approval-count {
    background: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.7rem;
    position: absolute;
    top: -5px;
    right: -5px;
    min-width: 18px;
    text-align: center;
}

/* User Management Modal */
.user-management-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    animation: fadeIn 0.3s ease;
}

.user-management-content {
    background-color: white;
    margin: 1% auto;
    padding: 0;
    border-radius: 12px;
    width: 95%;
    max-width: 1200px;
    max-height: 95vh;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    animation: slideIn 0.3s ease;
}

.user-management-header {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.user-management-header h3 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.user-management-body {
    padding: 25px;
    max-height: 75vh;
    overflow-y: auto;
}

.user-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 20px;
    flex-wrap: wrap;
}

.user-filters {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 16px;
    border: 2px solid #d4822a;
    background: transparent;
    color: #d4822a;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    transition: left 0.3s ease;
    z-index: -1;
}

.filter-btn.active::before,
.filter-btn:hover::before {
    left: 0;
}

.filter-btn.active,
.filter-btn:hover {
    color: white;
    border-color: #d4822a;
    transform: translateY(-2px);
}

.user-search-container {
    flex: 1;
    max-width: 400px;
    min-width: 250px;
}

.user-search-container .search-box {
    position: relative;
    width: 100%;
}

.user-search-container .search-box input {
    width: 100%;
    padding: 10px 40px 10px 40px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.user-search-container .search-box input:focus {
    outline: none;
    border-color: #d4822a;
    background: white;
    box-shadow: 0 0 0 3px rgba(212, 130, 42, 0.1);
}

.user-search-container .search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.user-search-container .search-box button {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.user-search-container .search-box button:hover {
    background: #e9ecef;
    color: #495057;
}

.users-table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.users-table th {
    background: linear-gradient(135deg, #fef9f3 0%, #fdf6ed 100%);
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    color: #8b4513;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #d4822a;
}

.users-table td {
    padding: 12px;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
    font-size: 0.9rem;
}

.users-table tbody tr {
    transition: all 0.2s ease;
}

.users-table tbody tr:hover {
    background: linear-gradient(135deg, #fef9f3 0%, #fdf6ed 100%);
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(212, 130, 42, 0.1);
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.user-name {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.user-email {
    color: #6c757d;
    font-size: 0.8rem;
}

.user-contact {
    color: #495057;
    font-size: 0.85rem;
}

.role-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    min-width: 60px;
    text-align: center;
}

.role-badge.donor {
    background: linear-gradient(135deg, #f0f8f0 0%, #e8f5e8 100%);
    color: #2e7d32;
    border: 1px solid #81c784;
}

.role-badge.admin {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    color: #d4822a;
    border: 1px solid #d4822a;
}

.action-buttons {
    display: flex;
    gap: 6px;
    justify-content: center;
}

.btn-promote,
.btn-demote {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 80px;
    justify-content: center;
}

.btn-promote {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-promote:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea080 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.btn-demote {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.btn-demote:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

/* Pagination Styles */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.pagination-info {
    color: #6c757d;
    font-size: 0.85rem;
}

.pagination {
    display: flex;
    gap: 5px;
    align-items: center;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.pagination button:hover:not(:disabled) {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
    border-color: #d4822a;
    transform: translateY(-1px);
}

.pagination button.active {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
    border-color: #d4822a;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-size-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    color: #6c757d;
}

.page-size-selector select {
    padding: 4px 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: white;
    font-size: 0.85rem;
}

/* Pending Approvals Styles */
.back-btn {
    background: #6c757d;
    color: white;
    padding: 8px 16px;
    border-radius: 5px;
    text-decoration: none;
    font-size: 0.9rem;
    margin-right: 10px;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.approvals-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.approval-card {
    animation: slideInUp 0.4s ease-out;
}

.approval-card:nth-child(2) {
    animation-delay: 0.1s;
}

.approval-card:nth-child(3) {
    animation-delay: 0.2s;
}

.approval-card:nth-child(4) {
    animation-delay: 0.3s;
}

.approval-card:nth-child(n+5) {
    animation-delay: 0.4s;
}

.approval-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border-left: 4px solid #d4822a;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.approval-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #d4822a 0%, #b8860b 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.approval-card:hover {
    box-shadow: 0 6px 20px rgba(212, 130, 42, 0.15);
    transform: translateY(-3px);
}

.approval-card:hover::before {
    opacity: 1;
}

.approval-header {
    display: grid;
    grid-template-columns: 1fr auto auto;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.action-info {
    min-width: 0; /* Allow text to wrap */
}

.action-info h4 {
    margin: 0 0 4px 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.action-meta {
    display: flex;
    gap: 15px;
    font-size: 0.85rem;
    color: #666;
    margin: 0;
}

.action-meta span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.action-id {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 6px 10px;
    border-radius: 6px;
    font-weight: 600;
    color: #495057;
    font-size: 0.85rem;
    white-space: nowrap;
}

.approval-actions {
    display: flex;
    gap: 8px;
}

.approval-body {
    margin-bottom: 15px;
}

.action-description {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 12px;
}

.action-description p {
    margin: 0;
    color: #495057;
    font-size: 0.9rem;
    line-height: 1.4;
}

.value-changes {
    display: flex;
    gap: 12px;
    margin-top: 12px;
}

.value-box {
    flex: 1;
    background: white;
    border-radius: 6px;
    padding: 10px;
    border: 1px solid #e9ecef;
    position: relative;
}

.value-box::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    border-radius: 3px 0 0 3px;
}

.old-values::before {
    background: #dc3545;
}

.new-values::before {
    background: #28a745;
}

.value-label {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.old-values .value-label {
    color: #dc3545;
}

.new-values .value-label {
    color: #28a745;
}

.value-content {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: #495057;
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    word-break: break-word;
}

.btn-approve,
.btn-reject {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.btn-approve {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-approve:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea080 100%);
    transform: translateY(-1px);
}

.btn-reject {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.btn-reject:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    transform: translateY(-1px);
}

.alert {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

@media (max-width: 768px) {
    .approval-header {
        grid-template-columns: 1fr;
        gap: 12px;
        text-align: center;
    }

    .action-meta {
        justify-content: center;
        flex-wrap: wrap;
    }

    .value-changes {
        flex-direction: column;
        gap: 10px;
    }

    .approval-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn-approve,
    .btn-reject {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }

    .approval-card {
        padding: 15px;
    }

    .action-info h4 {
        font-size: 1rem;
    }

    .action-meta {
        font-size: 0.8rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.approval-count {
    animation: pulse 2s infinite;
}

/* Responsive Design for User Management */
@media (max-width: 1024px) {
    .user-management-content {
        width: 98%;
        margin: 1% auto;
    }

    .user-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .user-search-container {
        max-width: none;
    }

    .users-table th:nth-child(3),
    .users-table td:nth-child(3) {
        display: none;
    }
}

@media (max-width: 768px) {
    .user-management-header {
        padding: 15px 20px;
    }

    .user-management-header h3 {
        font-size: 1.2rem;
    }

    .user-management-body {
        padding: 20px;
    }

    .user-filters {
        justify-content: center;
    }

    .filter-btn {
        flex: 1;
        text-align: center;
        min-width: 80px;
    }

    .users-table th,
    .users-table td {
        padding: 8px 6px;
        font-size: 0.8rem;
    }

    .users-table th:nth-child(1),
    .users-table td:nth-child(1) {
        width: 40px;
    }

    .user-name {
        font-size: 0.85rem;
    }

    .user-email {
        font-size: 0.75rem;
    }

    .role-badge {
        font-size: 0.7rem;
        padding: 4px 8px;
    }

    .btn-promote,
    .btn-demote {
        font-size: 0.7rem;
        padding: 4px 8px;
        min-width: 60px;
    }

    .pagination-container {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .pagination button {
        padding: 6px 10px;
        font-size: 0.8rem;
    }
}

.status-form.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    width: 12px;
    height: 12px;
    margin-top: -6px;
    border: 2px solid #d4822a;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Settings Page Styles */
.settings-container {
    padding: 0;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 25px;
    width: 100%;
    margin: 0;
}

.settings-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    height: fit-content;
}

.settings-section h2 {
    color: #333;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.4rem;
}

.settings-section h2 i {
    color: #d4822a;
}

.settings-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.settings-form .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.settings-form label {
    font-weight: 500;
    color: #333;
    font-size: 0.95rem;
}

.settings-form input {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.settings-form input:focus {
    outline: none;
    border-color: #d4822a;
    box-shadow: 0 0 0 3px rgba(212, 130, 42, 0.1);
}

.settings-form small {
    color: #6c757d;
    font-size: 0.85rem;
}

.settings-form .btn {
    align-self: flex-start;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.settings-form .btn-primary {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
}

.settings-form .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.settings-form .btn-secondary {
    background: #6c757d;
    color: white;
}

.settings-form .btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
}

.info-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-item label {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.9rem;
}

.info-item span {
    font-size: 1rem;
    color: #333;
}

.status-active {
    color: #28a745;
    display: flex;
    align-items: center;
    gap: 5px;
}

.success-message,
.error-message {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.success-message {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.back-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 15px;
    border-radius: 5px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

/* Settings Page Responsive */
@media (max-width: 1200px) {
    .settings-container {
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .settings-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .settings-section {
        padding: 20px;
    }

    .settings-section h2 {
        font-size: 1.2rem;
    }
}

/* Print styles */
@media print {
    .admin-header,
    .logout-btn,
    .status-form {
        display: none;
    }
    
    .admin-panel {
        background: white;
    }
    
    .bookings-table-container {
        box-shadow: none;
    }
    
    .stat-card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Additional responsive styles for new layout */
@media (max-width: 1024px) {
    .admin-layout {
        flex-direction: column;
    }

    .admin-sidebar {
        width: 100%;
        padding: 20px;
        border-right: none;
        border-bottom: 1px solid #e9ecef;
    }

    .sidebar-section h3 {
        font-size: 1rem;
        margin-bottom: 20px;
    }

    .stat-card {
        margin-bottom: 10px;
        padding: 15px;
    }

    .stat-icon {
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
    }

    .stat-info h4 {
        font-size: 1.3rem;
    }

    .admin-main-content {
        padding: 20px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .section-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .search-field {
        max-width: 100%;
    }
}

/* Modal Base Styles */
.modal {
    display: none !important;
    position: fixed !important;
    z-index: 9999 !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    backdrop-filter: blur(2px);
}

.modal.show {
    display: block !important;
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
    padding: 20px 25px;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-header .close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.modal-header .close:hover {
    opacity: 1;
}

.modal-body {
    padding: 25px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    background: #f8f9fa;
    padding: 15px 25px;
    border-radius: 0 0 8px 8px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    border-top: 1px solid #e9ecef;
}

/* Booking Details Modal */
.booking-details-modal {
    max-width: 900px;
    width: 90%;
}

.booking-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 20px;
}

.details-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #d4822a;
}

.details-section.full-width {
    grid-column: 1 / -1;
}

.details-section h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.details-section h4 i {
    color: #d4822a;
    font-size: 0.9rem;
}

.details-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.details-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.details-row .label {
    font-weight: 600;
    color: #555;
    flex: 1;
}

.details-row .value {
    color: #333;
    text-align: right;
    flex: 1;
}

.details-row .value.amount {
    font-size: 1.1rem;
    font-weight: bold;
    color: #28a745;
}

.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.badge-yes {
    background: #d4edda;
    color: #155724;
}

.badge-no {
    background: #f8d7da;
    color: #721c24;
}

.badge-monk {
    background: #fff3cd;
    color: #856404;
}

.badge-annual {
    background: #d1ecf1;
    color: #0c5460;
}

.info-subsection {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.info-subsection:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.info-subsection h5 {
    color: #495057;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-subsection h5 i {
    color: #6c757d;
    font-size: 0.8rem;
}

.info-content {
    background: white;
    padding: 12px;
    border-radius: 6px;
    color: #333;
    line-height: 1.5;
    font-size: 0.9rem;
}

.info-content.travel-support {
    border-left: 3px solid #28a745;
    background: #f8fff9;
}

.info-content.annual-event {
    border-left: 3px solid #17a2b8;
    background: #f1f9fc;
}

.info-content.monk-status {
    border-left: 3px solid #ffc107;
    background: #fffdf0;
}

.info-content.special-requests {
    border-left: 3px solid #6f42c1;
    background: #f8f7ff;
}

.no-requests {
    color: #6c757d;
    font-style: italic;
    margin: 0;
}

.details-btn {
    padding: 6px 10px;
    font-size: 0.8rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.details-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.receipt-link {
    color: #007bff;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.receipt-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

.no-receipt {
    color: #6c757d;
    font-style: italic;
}

.loading-spinner {
    text-align: center;
    padding: 40px;
    color: #666;
}

.loading-spinner i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #d4822a;
}

.error-message {
    text-align: center;
    padding: 40px;
    color: #dc3545;
}

.error-message i {
    font-size: 2rem;
    margin-bottom: 10px;
}



/* Responsive design for booking details modal */
@media (max-width: 768px) {
    .modal-content {
        margin: 10px;
        width: calc(100% - 20px);
        max-height: calc(100vh - 20px);
    }

    .booking-details-modal {
        width: 100%;
        margin: 0;
    }

    .modal-header {
        padding: 15px 20px;
    }

    .modal-header h3 {
        font-size: 1.1rem;
    }

    .modal-body {
        padding: 20px;
        max-height: calc(100vh - 200px);
    }

    .modal-footer {
        padding: 15px 20px;
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
        margin-bottom: 5px;
    }

    .booking-details-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .details-section {
        padding: 15px;
    }

    .details-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .details-row .value {
        text-align: left;
        font-weight: 600;
    }
}
