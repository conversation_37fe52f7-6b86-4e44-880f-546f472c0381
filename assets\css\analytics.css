/* Analytics Dashboard Styles */

.analytics-header {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
    padding: 25px 30px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.analytics-title h1 {
    margin: 0 0 8px 0;
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.analytics-title p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.95rem;
}

.analytics-filters {
    display: flex;
    gap: 20px;
    align-items: flex-end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 0.85rem;
    font-weight: 500;
    opacity: 0.9;
}

.filter-group select {
    padding: 8px 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.9rem;
    min-width: 140px;
}

.filter-group select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.2);
}

.filter-group select option {
    background: #333;
    color: white;
}

.refresh-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.refresh-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.analytics-content {
    padding: 0 30px 30px 30px;
}

/* Summary Cards - Compact Version */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.summary-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-left: 3px solid #d4822a;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(212, 130, 42, 0.12);
}

.card-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.card-content h3 {
    margin: 0 0 2px 0;
    font-size: 1.4rem;
    font-weight: 700;
    color: #333;
    line-height: 1.2;
}

.card-content p {
    margin: 0;
    color: #666;
    font-size: 0.8rem;
    font-weight: 500;
    line-height: 1.2;
}

/* Charts Carousel Section */
.charts-carousel {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.carousel-header {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.carousel-title h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.carousel-nav {
    display: flex;
    gap: 10px;
    align-items: center;
}

.carousel-indicators {
    display: flex;
    gap: 8px;
    margin-right: 15px;
}

.indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: white;
    transform: scale(1.2);
}

.nav-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.carousel-content {
    position: relative;
    height: 450px;
    overflow: hidden;
}

.chart-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 25px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s ease;
    display: flex;
    flex-direction: column;
}

.chart-slide.active {
    opacity: 1;
    transform: translateX(0);
}

.chart-slide.prev {
    transform: translateX(-100%);
}

.chart-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chart-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f1f3f4;
}

.chart-header h4 {
    margin: 0 0 5px 0;
    font-size: 1.1rem;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-header h4 i {
    color: #d4822a;
}

.chart-header p {
    margin: 0;
    color: #666;
    font-size: 0.85rem;
}

.chart-wrapper {
    position: relative;
    flex: 1;
    min-height: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-wrapper canvas {
    max-height: 100%;
    max-width: 100%;
}

/* Alert Styles */
.alert {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Loading State */
.chart-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #666;
}

.chart-loading i {
    font-size: 2rem;
    margin-bottom: 10px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Top Donors Table */
.table-wrapper {
    overflow-x: auto;
}

.donors-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.donors-table th,
.donors-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.donors-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    color: #495057;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.donors-table tbody tr:hover {
    background: linear-gradient(135deg, #fef9f3 0%, #fdf6ed 100%);
}

.rank-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    font-weight: 700;
    font-size: 0.8rem;
}

.rank-badge.rank-1 {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #8b4513;
}

.rank-badge.rank-2 {
    background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
    color: #495057;
}

.rank-badge.rank-3 {
    background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
    color: white;
}

.rank-badge:not(.rank-1):not(.rank-2):not(.rank-3) {
    background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%);
    color: white;
}

.count-badge {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.amount-badge {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Recent Activity */
.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: linear-gradient(135deg, #fef9f3 0%, #fdf6ed 100%);
    margin: 0 -15px;
    padding: 15px;
    border-radius: 8px;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.activity-icon.status-pending {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
}

.activity-icon.status-confirmed {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.activity-icon.status-completed {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.activity-icon.status-cancelled {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.activity-content {
    flex: 1;
}

.activity-title {
    margin-bottom: 5px;
    color: #333;
    font-size: 0.95rem;
}

.activity-details {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.activity-amount {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
}

.activity-status {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.activity-status.status-pending {
    background: #fff3cd;
    color: #856404;
}

.activity-status.status-confirmed {
    background: #d4edda;
    color: #155724;
}

.activity-status.status-completed {
    background: #d1ecf1;
    color: #0c5460;
}

.activity-status.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.activity-date {
    color: #6c757d;
    font-size: 0.8rem;
}

/* No Data State */
.no-data {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-data i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.no-data p {
    margin: 0;
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .carousel-content {
        height: 400px;
    }

    .chart-wrapper {
        min-height: 250px;
    }
}

@media (max-width: 768px) {
    .analytics-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }
    
    .analytics-filters {
        justify-content: space-between;
    }
    
    .filter-group {
        flex: 1;
        min-width: 120px;
    }
    
    .analytics-content {
        padding: 0 20px 20px 20px;
    }
    
    .summary-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .summary-card {
        padding: 12px;
    }

    .card-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .card-content h3 {
        font-size: 1.2rem;
    }

    .card-content p {
        font-size: 0.75rem;
    }
    
    .carousel-content {
        height: 350px;
    }

    .chart-slide {
        padding: 20px;
    }

    .chart-wrapper {
        min-height: 200px;
    }

    .carousel-header {
        padding: 15px 20px;
    }

    .carousel-title h3 {
        font-size: 1.1rem;
    }

    .nav-btn {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .analytics-title h1 {
        font-size: 1.4rem;
    }
    
    .analytics-filters {
        flex-direction: column;
        gap: 15px;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .filter-group select {
        width: 100%;
        min-width: auto;
    }
    
    .refresh-btn {
        width: 100%;
        justify-content: center;
    }

    .summary-cards {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .summary-card {
        padding: 10px;
        gap: 10px;
    }

    .card-icon {
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
    }

    .card-content h3 {
        font-size: 1.1rem;
    }

    .card-content p {
        font-size: 0.7rem;
    }

    .carousel-content {
        height: 300px;
    }

    .carousel-nav {
        flex-direction: column;
        gap: 8px;
    }

    .carousel-indicators {
        margin-right: 0;
        margin-bottom: 8px;
    }
}
