/* Step-by-Step Dhana Reservation Form Styles - Enhanced Spacing */

/* Override dashboard height constraints for reservation page */
.dashboard {
    height: auto !important;
    min-height: 100vh;
}

.dashboard-content {
    height: auto !important;
}

.reservation-container {
    max-width: calc(100vw - 400px);
    margin: 20px auto;
    margin-left: 200px;
    margin-right: 200px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: visible;
    min-height: auto;
}

/* Progress Indicator */
.progress-container {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    padding: 30px;
    color: white;
    border-radius: 15px 15px 0 0;
    margin-bottom: 10px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    margin-bottom: 30px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: white;
    width: 20%;
    transition: width 0.5s ease;
    border-radius: 3px;
}

.step-indicators {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.step-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0.6;
    transition: all 0.3s ease;
}

.step-indicator.active {
    opacity: 1;
    transform: scale(1.1);
}

.step-indicator.completed {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step-indicator.active .step-number,
.step-indicator.completed .step-number {
    background: white;
    color: #d4822a;
}

.step-label {
    font-size: 0.9rem;
    text-align: center;
    font-weight: 500;
}

/* Form Steps */
.step-form {
    padding: 15px 80px 50px 80px;
    border-radius: 0 0 15px 15px;
}

/* AGGRESSIVE STEP 3 SPACING FIXES */
.form-step[data-step="3"] {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

.form-step[data-step="3"] .step-header {
    margin-top: 0 !important;
    padding-top: 0 !important;
    margin-bottom: 15px !important;
}

.form-step[data-step="3"] .additional-info-section {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

.form-step[data-step="3"] .toggle-group:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Hide dhana-types-container completely in step 3 */
.form-step[data-step="3"] ~ #dhana-types-container,
.form-step.active[data-step="3"] ~ #dhana-types-container,
body:has(.form-step[data-step="3"].active) #dhana-types-container {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    overflow: hidden !important;
}

/* ULTRA-AGGRESSIVE: Hide dhana-types-container during step 3 using any selector possible */
.form-step.active[data-step="3"] + * #dhana-types-container,
.form-step.active[data-step="3"] ~ * #dhana-types-container,
body:has(.form-step.active[data-step="3"]) #dhana-types-container,
.booking-container:has(.form-step.active[data-step="3"]) #dhana-types-container,
.step-form:has(.form-step.active[data-step="3"]) #dhana-types-container {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    overflow: hidden !important;
    z-index: -9999 !important;
    pointer-events: none !important;
    max-height: 0 !important;
    max-width: 0 !important;
    min-height: 0 !important;
    min-width: 0 !important;
}

/* ULTRA-AGGRESSIVE: Hide step 2 when step 3 is active to prevent phantom spacing */
body:has(.form-step.active[data-step="3"]) .form-step[data-step="2"],
.booking-container:has(.form-step.active[data-step="3"]) .form-step[data-step="2"],
.step-form:has(.form-step.active[data-step="3"]) .form-step[data-step="2"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    overflow: hidden !important;
    z-index: -9999 !important;
    pointer-events: none !important;
    max-height: 0 !important;
    max-width: 0 !important;
    min-height: 0 !important;
    min-width: 0 !important;
}

.form-step {
    display: none !important;
    animation: fadeInUp 0.5s ease;
    position: relative;
    min-height: auto;
}

.form-step.active {
    display: block !important;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-header {
    text-align: center;
    margin-bottom: 20px;
}

.step-header h3 {
    color: #333;
    font-size: 1.1rem;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.step-header h3 i {
    color: #d4822a;
}

.step-header p {
    color: #666;
    font-size: 0.85rem;
    margin-bottom: 0;
}

/* Dhana Types Grid */
.dhana-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.loading-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px;
    color: #666;
}

.loading-message i {
    font-size: 24px;
    margin-bottom: 10px;
    color: #d4822a;
}

.dhana-type-option {
    position: relative;
}

.dhana-type-card {
    position: relative;
    min-height: 130px;
}

.dhana-type-card input[type="radio"] {
    display: none;
}

.dhana-type-label {
    display: block;
    padding: 20px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    position: relative;
    height: 100%;
}

.dhana-type-label:hover {
    border-color: #d4822a;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(212, 130, 42, 0.1);
}

.dhana-type-card input[type="radio"]:checked + .dhana-type-label {
    border-color: #d4822a;
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 130, 42, 0.3);
}

.dhana-type-card input[type="radio"]:checked + .dhana-type-label .dhana-type-header h4 {
    color: white;
}

.dhana-type-card input[type="radio"]:checked + .dhana-type-label .dhana-type-price {
    color: rgba(255, 255, 255, 0.8);
    border-top-color: rgba(255, 255, 255, 0.2);
}

.dhana-type-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 12px;
    gap: 8px;
}

.dhana-type-header h4 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 700;
    line-height: 1.2;
    color: #2c3e50;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.3px;
}

.dhana-type-price {
    font-weight: 500;
    font-size: 0.95rem;
    color: #666;
    opacity: 0.8;
    padding-top: 4px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    margin-top: 4px;
}

.coming-soon {
    color: #ffc107;
    font-size: 0.9rem;
    font-weight: normal;
}

.dhana-type-description {
    margin: 0 0 12px 0;
    font-size: 0.85rem;
    opacity: 0.7;
    line-height: 1.4;
}

.time-slot-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    opacity: 0.8;
}

.disabled-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(248, 249, 250, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    color: #6c757d;
    font-weight: 500;
}

.disabled-overlay i {
    font-size: 2rem;
    margin-bottom: 10px;
}

/* Date & Time Selection */
.date-time-selection {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

/* Enhanced form labels for date and time selection */
.date-time-selection .form-group label {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.date-time-selection .form-group label::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    border-radius: 2px;
}

/* Form group enhancements */
.date-time-selection .form-group {
    position: relative;
}

/* Enhanced Time Slot Select Styling to match Date Input */
select#booking_time_slot {
    width: 100%;
    padding: 20px 24px;
    font-size: 1.1rem;
    border: 2px solid #e1e5e9;
    border-radius: 15px;
    background: #f5f5f5;
    color: #333;
    font-family: inherit;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    min-height: 64px;
    height: 64px;
    box-sizing: border-box;
    font-weight: 500;
    letter-spacing: 0.5px;
    line-height: 1.2;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: none;
    text-align-last: left;
}

select#booking_time_slot:hover {
    border-color: #d4822a;
    box-shadow: 0 4px 12px rgba(212, 130, 42, 0.15);
    transform: translateY(-1px);
}

select#booking_time_slot:focus {
    outline: none;
    border-color: #d4822a;
    box-shadow: 0 0 0 4px rgba(212, 130, 42, 0.15);
    background: #fafbff;
    transform: translateY(-1px);
}

/* Time slot option styling */
select#booking_time_slot option {
    padding: 10px 16px;
    font-size: 1rem;
    font-weight: 500;
    color: #333;
    background: #f5f5f5;
    line-height: 1.4;
    min-height: 40px;
    display: flex;
    align-items: center;
}

select#booking_time_slot option:first-child {
    color: #999;
    font-style: italic;
}

select#booking_time_slot option:hover {
    background: #f5f5f5;
}

/* Custom wrapper for time slot select */
.custom-time-slot-picker {
    position: relative;
    display: block;
    width: 100%;
}

.custom-time-slot-picker::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 15px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 0;
}

.custom-time-slot-picker:hover::before {
    opacity: 1;
}

.custom-time-slot-picker select {
    position: relative;
    z-index: 1;
    background: transparent;
}

/* Time slot active state */
.custom-time-slot-picker.time-slot-active {
    z-index: 1000;
}

.custom-time-slot-picker.time-slot-active::before {
    opacity: 1;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.custom-time-slot-picker.time-slot-opening select {
    box-shadow: 0 0 0 4px rgba(212, 130, 42, 0.2);
    border-color: #d4822a;
    transform: scale(1.02);
}

/* Animation for time slot selection */
@keyframes timeSlotSelected {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.custom-time-slot-picker select.time-slot-selected {
    animation: timeSlotSelected 0.4s ease;
}

/* Remove custom dropdown arrow to prevent conflicts */

/* Simplified time slot picker without tooltip conflicts */

/* Enhanced Date Input Styling */
input[type="date"] {
    width: 100%;
    padding: 20px 60px 20px 24px;
    font-size: 1.2rem;
    border: 2px solid #e1e5e9;
    border-radius: 15px;
    background: #f5f5f5;
    color: #333;
    font-family: inherit;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    min-height: 64px;
    height: 64px;
    box-sizing: border-box;
    font-weight: 500;
    letter-spacing: 0.5px;
}

input[type="date"]:hover {
    border-color: #d4822a;
    box-shadow: 0 4px 12px rgba(212, 130, 42, 0.15);
    transform: translateY(-1px);
}

input[type="date"]:focus {
    outline: none;
    border-color: #d4822a;
    box-shadow: 0 0 0 4px rgba(212, 130, 42, 0.15);
    background: #fafbff;
    transform: translateY(-1px);
}

/* Calendar icon styling */
input[type="date"]::-webkit-calendar-picker-indicator {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="%23667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>') no-repeat center;
    background-size: 20px 20px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    margin-right: 8px;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
}

/* Firefox date input styling */
input[type="date"]::-moz-focus-inner {
    border: 0;
}

/* Date input placeholder styling */
input[type="date"]:invalid {
    color: #999;
}

input[type="date"]:valid {
    color: #333;
}

/* Custom Calendar Popup Styling */
.custom-date-picker {
    position: relative;
    width: 100%;
}

.custom-date-picker input[type="date"] {
    position: relative;
    z-index: 2;
}

/* Style the native calendar popup when it appears */
input[type="date"]:focus::-webkit-calendar-picker-indicator,
input[type="date"]:active::-webkit-calendar-picker-indicator {
    background-color: rgba(102, 126, 234, 0.2);
    border-radius: 6px;
    transform: scale(1.1);
}

/* Enhanced calendar dropdown for better visibility */
@supports (-webkit-appearance: none) {
    input[type="date"]::-webkit-calendar-picker-indicator {
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        width: 24px;
        height: 24px;
        background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%23667eea" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>') no-repeat center;
        background-size: 24px 24px;
        cursor: pointer;
        opacity: 0.8;
        transition: all 0.3s ease;
        border-radius: 6px;
        padding: 2px;
    }

    input[type="date"]:hover::-webkit-calendar-picker-indicator {
        opacity: 1;
        background-color: rgba(102, 126, 234, 0.1);
        transform: translateY(-50%) scale(1.05);
    }

    input[type="date"]:focus::-webkit-calendar-picker-indicator {
        opacity: 1;
        background-color: rgba(102, 126, 234, 0.15);
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    }
}

/* Additional Calendar Dropdown Enhancements */
/* Webkit Calendar Popup Styling */
input[type="date"]::-webkit-calendar-picker-indicator {
    position: relative;
    z-index: 10;
}

/* Try to style the calendar popup itself (limited support) */
input[type="date"]::-webkit-inner-spin-button,
input[type="date"]::-webkit-clear-button {
    display: none;
}

/* Enhanced date input container */
.custom-date-picker {
    position: relative;
    display: block;
    width: 100%;
}

.custom-date-picker::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 0;
}

.custom-date-picker:hover::before {
    opacity: 1;
}

.custom-date-picker input[type="date"] {
    position: relative;
    z-index: 1;
    background: transparent;
}

/* Date input focus state enhancement */
.custom-date-picker input[type="date"]:focus {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

/* Calendar icon enhancement */
.custom-date-picker input[type="date"]::-webkit-calendar-picker-indicator {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%23667eea" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="3" ry="3"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line><circle cx="8" cy="14" r="1"></circle><circle cx="12" cy="14" r="1"></circle><circle cx="16" cy="14" r="1"></circle><circle cx="8" cy="18" r="1"></circle><circle cx="12" cy="18" r="1"></circle></svg>') no-repeat center;
    background-size: 24px 24px;
    width: 32px;
    height: 32px;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    opacity: 0.8;
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 4px;
}

.custom-date-picker input[type="date"]:hover::-webkit-calendar-picker-indicator {
    opacity: 1;
    background-color: rgba(102, 126, 234, 0.1);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.custom-date-picker input[type="date"]:focus::-webkit-calendar-picker-indicator {
    opacity: 1;
    background-color: rgba(102, 126, 234, 0.15);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
}

/* Animation for date selection */
@keyframes dateSelected {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.custom-date-picker input[type="date"].date-selected {
    animation: dateSelected 0.4s ease;
}

/* Experimental: Try to style the calendar popup (limited browser support) */
/* These styles may work in some Webkit browsers */
input[type="date"]::-webkit-calendar-picker-indicator:hover + ::-webkit-calendar-picker-indicator {
    transform: scale(1.2);
}

/* Try to increase the size of the calendar popup */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    input[type="date"]::-webkit-calendar-picker-indicator {
        font-size: 18px;
        width: 36px;
        height: 36px;
        background-size: 28px 28px;
    }
}

/* Alternative approach: Add a tooltip-like enhancement */
.custom-date-picker::after {
    content: 'Click to open calendar';
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.custom-date-picker:hover::after {
    opacity: 1;
}

.custom-date-picker input[type="date"]:focus + ::after,
.custom-date-picker input[type="date"]:valid + ::after {
    display: none;
}

/* Calendar active state */
.custom-date-picker.calendar-active {
    z-index: 1000;
}

.custom-date-picker.calendar-active::before {
    opacity: 1;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.custom-date-picker.calendar-opening input[type="date"] {
    box-shadow: 0 0 0 4px rgba(212, 130, 42, 0.2);
    border-color: #d4822a;
}

.custom-date-picker.calendar-opening input[type="date"]::-webkit-calendar-picker-indicator {
    transform: translateY(-50%) scale(1.2);
    background-color: rgba(102, 126, 234, 0.2);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* Enhanced mobile date picker and time slot picker */
@media (max-width: 768px) {
    .custom-date-picker input[type="date"] {
        font-size: 1.2rem;
        padding: 18px 60px 18px 20px;
        min-height: 60px;
        height: 60px;
    }

    .custom-date-picker input[type="date"]::-webkit-calendar-picker-indicator {
        width: 40px;
        height: 40px;
        background-size: 32px 32px;
        right: 10px;
    }

    .custom-time-slot-picker select#booking_time_slot {
        font-size: 1.1rem;
        padding: 16px 20px 16px 20px;
        min-height: 60px;
        height: 60px;
        line-height: 1.3;
    }

    .date-time-selection {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    /* Mobile responsive toggle switch */
    .toggle-group {
        flex-direction: column;
        text-align: center;
        padding: 20px;
        gap: 20px;
    }

    .toggle-content {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .toggle-content h4 {
        font-size: 1.1rem;
        justify-content: center;
    }

    .toggle-switch-container {
        align-self: center;
    }

    /* Mobile responsive for Step 4 additional info */
    .additional-info-section .toggle-group {
        margin-bottom: 15px;
    }

    .special-requests-group {
        margin-top: 20px;
    }
}

/* Custom calendar dropdown styling (Webkit browsers) */
input[type="date"]::-webkit-datetime-edit {
    padding: 0;
    font-size: 1.2rem;
    color: #333;
    font-weight: 500;
}

input[type="date"]::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
}

input[type="date"]::-webkit-datetime-edit-text {
    color: #666;
    padding: 0 6px;
    font-size: 1.1rem;
}

input[type="date"]::-webkit-datetime-edit-year-field,
input[type="date"]::-webkit-datetime-edit-month-field,
input[type="date"]::-webkit-datetime-edit-day-field {
    font-size: 1.2rem;
    font-weight: 500;
    color: #333;
    padding: 2px 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

input[type="date"]::-webkit-datetime-edit-year-field:focus,
input[type="date"]::-webkit-datetime-edit-month-field:focus,
input[type="date"]::-webkit-datetime-edit-day-field:focus {
    background-color: rgba(102, 126, 234, 0.1);
    outline: none;
}

/* Enhanced Calendar Dropdown Styling */
input[type="date"]::-webkit-calendar-picker-indicator {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="%23667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>') no-repeat center;
    background-size: 20px 20px;
    width: 28px;
    height: 28px;
    cursor: pointer;
    margin-right: 8px;
    opacity: 0.7;
    transition: opacity 0.3s ease;
    border-radius: 4px;
    padding: 4px;
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
    background-color: rgba(102, 126, 234, 0.1);
}

/* Calendar dropdown panel styling (limited browser support) */
input[type="date"]::-webkit-calendar-picker-indicator:active {
    transform: scale(0.95);
}

/* Try to style the calendar dropdown */
::-webkit-calendar-picker-indicator {
    filter: invert(0.5) sepia(1) saturate(5) hue-rotate(240deg);
}

/* Alternative approach - Custom date picker overlay */
.date-picker-overlay {
    position: relative;
    display: inline-block;
    width: 100%;
}

.date-picker-overlay input[type="date"] {
    position: relative;
    z-index: 1;
}

/* Enhanced styling for better calendar appearance */
input[type="date"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: #f5f5f5;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    padding: 16px 50px 16px 20px;
    font-size: 1.1rem;
    color: #333;
    width: 100%;
    min-height: 56px;
    box-sizing: border-box;
    font-family: inherit;
    transition: all 0.3s ease;
    cursor: pointer;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="%23667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>');
    background-repeat: no-repeat;
    background-position: right 16px center;
    background-size: 20px 20px;
}

input[type="date"]::-webkit-datetime-edit-month-field,
input[type="date"]::-webkit-datetime-edit-day-field,
input[type="date"]::-webkit-datetime-edit-year-field {
    background: transparent;
    color: #333;
    font-weight: 500;
    padding: 2px 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

input[type="date"]::-webkit-datetime-edit-month-field:hover,
input[type="date"]::-webkit-datetime-edit-day-field:hover,
input[type="date"]::-webkit-datetime-edit-year-field:hover {
    background: rgba(102, 126, 234, 0.1);
}

input[type="date"]::-webkit-datetime-edit-month-field:focus,
input[type="date"]::-webkit-datetime-edit-day-field:focus,
input[type="date"]::-webkit-datetime-edit-year-field:focus {
    background: rgba(102, 126, 234, 0.2);
    outline: none;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #d4822a;
    box-shadow: 0 0 0 3px rgba(212, 130, 42, 0.1);
}

.availability-checker {
    grid-column: 1 / -1;
    text-align: center;
    margin: 15px 0 10px 0;
}

.availability-result {
    margin-top: 10px;
    padding: 15px 20px;
    border-radius: 12px;
    display: none;
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.availability-result.available {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 2px solid #28a745;
    display: block;
    animation: slideInSuccess 0.5s ease;
}

.availability-result.unavailable {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 2px solid #dc3545;
    display: block;
    animation: slideInError 0.5s ease;
}

.availability-result i {
    font-size: 1.3rem;
    margin-right: 10px;
    vertical-align: middle;
}

/* Animations for availability results */
@keyframes slideInSuccess {
    from {
        opacity: 0;
        transform: translateY(-10px);
        background: #e8f5e8;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    }
}

@keyframes slideInError {
    from {
        opacity: 0;
        transform: translateY(-10px);
        background: #f8e8e8;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    }
}

/* Loading state for availability check */
.availability-result:not(.available):not(.unavailable) {
    background: #f5f5f5;
    color: #6c757d;
    border: 2px solid #dee2e6;
    display: block;
}

.availability-result:not(.available):not(.unavailable) i.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Next button disabled state during availability check */
#nextBtn:disabled {
    background: #6c757d !important;
    cursor: not-allowed !important;
    opacity: 0.7;
}

#nextBtn:disabled:hover {
    background: #6c757d !important;
    transform: none !important;
    box-shadow: none !important;
}

.calendar-link {
    grid-column: 1 / -1;
    text-align: center;
}

/* Checkbox Groups */
.checkbox-group {
    margin-bottom: 25px;
}

.checkbox-group input[type="checkbox"] {
    display: none;
}

.checkbox-label {
    display: block;
    padding: 20px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.checkbox-label:hover {
    border-color: #d4822a;
    background: rgba(212, 130, 42, 0.05);
}

.checkbox-group input[type="checkbox"]:checked + .checkbox-label {
    border-color: #d4822a;
    background: rgba(212, 130, 42, 0.1);
}

.checkbox-content h4 {
    margin: 0 0 8px 0;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.checkbox-content h4 i {
    color: #d4822a;
}

.checkbox-content p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* Toggle Switch Styling */
.toggle-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f5f5f5;
    border: 2px solid #e1e5e9;
    border-radius: 15px;
    padding: 25px 30px;
    margin-bottom: 25px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.toggle-group:hover {
    border-color: #d4822a;
    box-shadow: 0 4px 12px rgba(212, 130, 42, 0.15);
    transform: translateY(-1px);
}

.toggle-content {
    flex: 1;
    margin-right: 30px;
}

.toggle-content h4 {
    color: #333;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.toggle-content h4 i {
    color: #d4822a;
    font-size: 1.1rem;
}

.toggle-content p {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
    margin: 0;
}

/* Toggle Switch Container */
.toggle-switch-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

/* Hide the default checkbox */
.toggle-switch-input {
    display: none;
}

/* Toggle Switch Label */
.toggle-switch-label {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    user-select: none;
}

/* Toggle Switch Slider */
.toggle-switch-slider {
    position: relative;
    width: 60px;
    height: 30px;
    background: #ccc;
    border-radius: 30px;
    transition: all 0.3s ease;
    display: block;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Toggle Switch Button */
.toggle-switch-button {
    position: absolute;
    top: 3px;
    left: 3px;
    width: 24px;
    height: 24px;
    background: #f5f5f5;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    display: block;
}

/* Toggle Switch Text */
.toggle-switch-text {
    font-size: 0.8rem;
    font-weight: 600;
    color: #666;
    transition: all 0.3s ease;
}

.toggle-on {
    display: none;
}

.toggle-off {
    display: inline;
}

/* Active State (Checked) */
.toggle-switch-input:checked + .toggle-switch-label .toggle-switch-slider {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 10px rgba(212, 130, 42, 0.3);
}

.toggle-switch-input:checked + .toggle-switch-label .toggle-switch-button {
    transform: translateX(30px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.toggle-switch-input:checked + .toggle-switch-label .toggle-switch-text .toggle-on {
    display: inline;
    color: #d4822a;
}

.toggle-switch-input:checked + .toggle-switch-label .toggle-switch-text .toggle-off {
    display: none;
}

/* Hover Effects */
.toggle-switch-label:hover .toggle-switch-slider {
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 8px rgba(102, 126, 234, 0.2);
}

.toggle-switch-input:checked + .toggle-switch-label:hover .toggle-switch-slider {
    background: linear-gradient(135deg, #b8860b 0%, #a0522d 100%);
}

/* Focus State for Accessibility */
.toggle-switch-input:focus + .toggle-switch-label .toggle-switch-slider {
    outline: 2px solid #d4822a;
    outline-offset: 2px;
}

/* Animation for Toggle */
@keyframes toggleOn {
    0% { transform: translateX(0) scale(1); }
    50% { transform: translateX(15px) scale(1.1); }
    100% { transform: translateX(30px) scale(1); }
}

@keyframes toggleOff {
    0% { transform: translateX(30px) scale(1); }
    50% { transform: translateX(15px) scale(1.1); }
    100% { transform: translateX(0) scale(1); }
}

@keyframes togglePulse {
    0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
    100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
}

.toggle-switch-input:checked + .toggle-switch-label .toggle-switch-button {
    animation: toggleOn 0.3s ease;
}

.toggle-switch-input:not(:checked) + .toggle-switch-label .toggle-switch-button {
    animation: toggleOff 0.3s ease;
}

/* Pulse effect when toggled */
.toggle-switch-input:checked + .toggle-switch-label .toggle-switch-slider {
    animation: togglePulse 0.6s ease-out;
}

/* Enhanced visual feedback */
.toggle-group.toggle-activated {
    transform: scale(1.02);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.2);
}

/* Additional spacing for Step 4 elements */
.special-requests-group {
    margin-top: 25px;
}

/* Monk toggle specific styling */
.monk-toggle {
    position: relative;
}

/* Additional info section spacing */
.additional-info-section .toggle-group:not(:last-of-type) {
    margin-bottom: 20px;
}

/* Info and Warning Boxes */
.info-box,
.warning-box {
    display: flex;
    gap: 15px;
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
}

.info-box {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.warning-box {
    background: #fff3e0;
    border-left: 4px solid #ff9800;
    transition: all 0.3s ease;
    transform: translateY(-10px);
    opacity: 0;
}

.warning-box[style*="block"] {
    transform: translateY(0);
    opacity: 1;
    animation: slideInWarning 0.4s ease-out;
}

@keyframes slideInWarning {
    0% {
        transform: translateY(-20px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.info-box i,
.warning-box i {
    font-size: 1.5rem;
    margin-top: 2px;
}

.info-box i {
    color: #2196f3;
}

.warning-box i {
    color: #ff9800;
}

.info-box h5,
.warning-box h5 {
    margin: 0 0 8px 0;
    color: #333;
}

.info-box p,
.warning-box p {
    margin: 0 0 8px 0;
    color: #666;
    line-height: 1.5;
}

.warning-box p:last-child {
    margin-bottom: 0;
}

/* Review Section - Compact Design */
.booking-review {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    max-width: 600px;
    margin: 0 auto;
}

.review-section {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.review-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.review-section h4 {
    color: #d4822a;
    margin-bottom: 12px;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-left: 3px solid #d4822a;
    padding-left: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f8f9fa;
}

.review-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 6px 0;
    font-size: 0.9rem;
    border-bottom: 1px solid #f8f9fa;
}

.review-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.review-item .label {
    font-weight: 500;
    color: #495057;
    margin-right: 15px;
    white-space: nowrap;
    min-width: 120px;
}

.review-item .value {
    color: #212529;
    font-weight: 600;
}

.total-amount {
    font-size: 1.3rem;
    font-weight: bold;
    color: white;
    text-align: center;
    padding: 12px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 6px;
    border: none;
    box-shadow: 0 3px 8px rgba(40, 167, 69, 0.3);
    margin: 12px 0;
}

/* Review Navigation Buttons */
.review-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.review-navigation .btn {
    min-width: 150px;
    padding: 12px 24px;
    font-size: 0.95rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.review-navigation .btn:first-child {
    margin-right: auto;
}

.review-navigation .btn:last-child {
    margin-left: auto;
}

.review-navigation .btn-secondary {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
}

.review-navigation .btn-secondary:hover {
    background: #5a6268;
    border-color: #545b62;
    transform: translateY(-1px);
}

.review-navigation .btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-color: #28a745;
    color: white;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.review-navigation .btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    border-color: #1e7e34;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.special-requests-text {
    background: white;
    padding: 10px;
    border-radius: 6px;
    color: #333;
    font-style: italic;
    font-size: 0.85rem;
    border-left: 3px solid #17a2b8;
}

/* Navigation Buttons */
.form-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #e9ecef;
    gap: 20px;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

.form-navigation .btn {
    min-width: 120px;
    max-width: 200px;
    width: auto !important;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    flex-shrink: 1;
    padding: 0 20px;
    box-sizing: border-box;
    margin-bottom: 0 !important;
}

.btn-outline {
    background: transparent;
    border: 2px solid #d4822a;
    color: #d4822a;
}

.btn-outline:hover {
    background: #d4822a;
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea080 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .booking-container {
        margin: 0 15px;
        max-width: none;


    }

    .step-form {
        padding: 10px 40px 30px 40px;
    }
    
    .progress-container {
        padding: 20px;
        margin-bottom: 8px;
    }


    
    .step-indicators {
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .step-label {
        font-size: 0.8rem;
    }
    
    .dhana-types-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .date-time-selection {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    /* Mobile date input adjustments */
    input[type="date"] {
        padding: 14px 16px;
        font-size: 1rem;
        min-height: 52px;
    }

    input[type="date"]::-webkit-calendar-picker-indicator {
        background-size: 18px 18px;
        width: 20px;
        height: 20px;
    }
    
    .form-navigation {
        flex-direction: column;
        gap: 15px;
    }
    
    .form-navigation .btn {
        width: 100%;
    }
    
    .review-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 3px;
        margin-bottom: 8px;
        padding: 6px 0;
    }

    .review-item .value {
        text-align: left;
        font-weight: 600;
    }

    .review-section h4 {
        font-size: 0.9rem;
        margin-bottom: 8px;
    }
}

@media (max-width: 480px) {
    .step-form {
        padding: 8px 30px 25px 30px;
    }

    .step-header h3 {
        font-size: 1.3rem;
        flex-direction: column;
        gap: 5px;
    }
    
    .dhana-type-label {
        padding: 18px;
    }

    .dhana-type-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
        margin-bottom: 10px;
    }

    .dhana-type-header h4 {
        font-size: 1.3rem;
    }

    .dhana-type-price {
        font-size: 0.9rem;
    }


}
    
    .booking-review {
        padding: 15px;
        max-width: 100%;
        margin: 0;
    }

    .review-section {
        margin-bottom: 15px;
        padding-bottom: 10px;
    }

    .total-amount {
        font-size: 1.1rem;
        padding: 8px;
    }

    .review-navigation {
        flex-direction: row;
        justify-content: space-between;
        gap: 15px;
        margin-top: 15px;
        padding-top: 15px;
    }

    .review-navigation .btn {
        min-width: 120px;
        padding: 10px 16px;
        font-size: 0.9rem;
    }
}

/* Mobile styles for very small screens */
@media (max-width: 480px) {
    .review-navigation {
        flex-direction: column;
        gap: 12px;
        margin-top: 15px;
        padding-top: 15px;
    }

    .review-navigation .btn {
        width: 100%;
        min-width: auto;
        padding: 12px 20px;
    }

    .review-navigation .btn:first-child,
    .review-navigation .btn:last-child {
        margin: 0;
    }
    
    .step-number {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
}
