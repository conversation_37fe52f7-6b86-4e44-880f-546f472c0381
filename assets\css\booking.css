/* Dāna Reservation Form Styles for Dhana Reservation System */

.reservation-form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.reservation-form {
    max-width: 800px;
    margin: 0 auto;
}

/* Form Sections */
.form-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #e9ecef;
}

.form-section:last-of-type {
    border-bottom: none;
}

.form-section h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3 i {
    color: #d4822a;
}

/* Dhana Types Grid */
.dhana-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.dhana-type-card {
    position: relative;
}

.dhana-type-card input[type="radio"] {
    display: none;
}

.dhana-type-label {
    display: block;
    padding: 20px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    position: relative;
}

.dhana-type-label:hover {
    border-color: #d4822a;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(212, 130, 42, 0.1);
}

.dhana-type-label.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #f5f5f5;
}

.dhana-type-label.disabled:hover {
    transform: none;
    box-shadow: none;
    border-color: #e1e5e9;
}

.dhana-type-card input[type="radio"]:checked + .dhana-type-label {
    border-color: #d4822a;
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
}

.dhana-type-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.dhana-type-header h4 {
    margin: 0;
    font-size: 1.1rem;
}

.dhana-type-price {
    font-weight: bold;
    font-size: 1.1rem;
}

.coming-soon {
    color: #ffc107;
    font-size: 0.9rem;
    font-weight: normal;
}

.dhana-type-description {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

.disabled-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(248, 249, 250, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    color: #6c757d;
    font-weight: 500;
}

.disabled-overlay i {
    font-size: 2rem;
    margin-bottom: 10px;
}

/* Date Selection */
.date-selection {
    display: flex;
    gap: 20px;
    align-items: flex-end;
}

.date-selection .form-group {
    flex: 1;
}

.calendar-link {
    flex-shrink: 0;
}

.date-availability {
    margin-top: 15px;
    padding: 15px;
    border-radius: 8px;
    display: none;
}

.date-availability.available {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.date-availability.unavailable {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #d4822a;
    box-shadow: 0 0 0 3px rgba(212, 130, 42, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Dāna Reservation Summary */
.reservation-summary {
    background: #f5f5f5;
    padding: 25px;
    border-radius: 10px;
    margin: 30px 0;
    border-left: 4px solid #d4822a;
}

.booking-summary h3 {
    margin-bottom: 20px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.booking-summary h3 i {
    color: #d4822a;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.summary-row:last-child {
    border-bottom: none;
}

.summary-row.total {
    font-weight: bold;
    font-size: 1.1rem;
    color: #333;
    margin-top: 10px;
    padding-top: 15px;
    border-top: 2px solid #d4822a;
}

/* Form Actions */
.form-actions {
    text-align: center;
    margin-top: 30px;
}

.btn-large {
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #d4822a;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .reservation-form-container {
        padding: 20px;
    }
    
    .dhana-types-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .date-selection {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    
    .summary-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .summary-row span:last-child {
        font-weight: 600;
    }
    
    .form-section h3 {
        font-size: 1.2rem;
    }
    
    .btn-large {
        width: 100%;
        padding: 12px 20px;
    }
}

@media (max-width: 480px) {
    .reservation-form-container {
        padding: 15px;
    }
    
    .dhana-type-label {
        padding: 15px;
    }
    
    .dhana-type-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .reservation-summary {
        padding: 20px;
    }
    
    .form-section {
        margin-bottom: 30px;
        padding-bottom: 20px;
    }
}

/* Animation for form sections */
.form-section {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Validation styles */
.form-group.error input,
.form-group.error textarea,
.form-group.error select {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-group.success input,
.form-group.success textarea,
.form-group.success select {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.field-error {
    color: #dc3545;
    font-size: 0.8rem;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.field-success {
    color: #28a745;
    font-size: 0.8rem;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}
