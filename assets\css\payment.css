/* Payment Page Styles for Dhana Booking System - Compact & Beautiful Design */

/* Override dashboard layout for payment page to allow scrolling */
html, body {
    overflow-y: auto !important;
    height: auto !important;
    max-height: none !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

body .dashboard {
    height: auto !important;
    min-height: 100vh;
    overflow: visible !important;
    display: block !important;
    background: transparent;
}

body .dashboard-content {
    overflow: visible !important;
    height: auto !important;
    max-height: none !important;
}

/* Three-Column Payment Container */
.payment-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 15px;
    padding-bottom: 30px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    align-items: start;
}

/* Responsive layout for smaller screens */
@media (max-width: 1200px) {
    .payment-container {
        grid-template-columns: 1fr 1fr;
        max-width: 1000px;
    }

    .payment-actions-container {
        grid-column: 1 / -1;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }
}

@media (max-width: 968px) {
    .payment-container {
        grid-template-columns: 1fr;
        max-width: 800px;
        gap: 15px;
    }

    .payment-actions-container {
        grid-column: 1;
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
}

/* Compact Booking Summary Card */
.booking-summary-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(212, 130, 42, 0.1);
    border: 1px solid rgba(212, 130, 42, 0.2);
    position: relative;
    overflow: hidden;
}

.booking-summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #d4822a 0%, #b8860b 100%);
}

.booking-summary-card h3 {
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
    font-weight: 600;
}

.booking-summary-card h3 i {
    color: #d4822a;
    font-size: 1rem;
}

/* Compact Summary Grid */
.summary-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px 15px;
    margin-bottom: 15px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.summary-item label {
    font-weight: 500;
    color: #666;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-item span {
    color: #333;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Compact Total Amount */
.summary-item.total {
    grid-column: 1 / -1;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    border-radius: 8px;
    margin-top: 8px;
    color: white;
}

.summary-item.total label,
.summary-item.total span {
    font-size: 1rem;
    font-weight: 700;
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Compact Special Requests */
.special-requests {
    margin-top: 12px;
    padding: 12px;
    background: rgba(255, 193, 7, 0.1);
    border-radius: 6px;
    border-left: 3px solid #ffc107;
}

.special-requests label {
    font-weight: 500;
    color: #666;
    display: block;
    margin-bottom: 5px;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.special-requests p {
    margin: 0;
    color: #333;
    line-height: 1.4;
    font-size: 0.9rem;
}

/* Individual column containers will be styled separately */

/* Compact Payment Instructions */
.payment-instructions {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.payment-instructions h3 {
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
    font-weight: 600;
}

.payment-instructions h3 i {
    color: #d4822a;
    font-size: 1rem;
}

/* Compact Instruction Steps */
.instruction-step {
    display: flex;
    gap: 12px;
    margin-bottom: 15px;
    align-items: flex-start;
}

.instruction-step:last-child {
    margin-bottom: 0;
}

.step-number {
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
    font-size: 0.9rem;
    box-shadow: 0 2px 8px rgba(212, 130, 42, 0.3);
}

.step-content h4 {
    margin: 0 0 6px 0;
    color: #333;
    font-size: 1rem;
    font-weight: 600;
}

.step-content p {
    margin: 0 0 8px 0;
    color: #666;
    line-height: 1.4;
    font-size: 0.9rem;
}

/* Beautiful Bank Details Card */
.bank-details-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.2);
    position: relative;
    overflow: hidden;
    margin: 15px 0;
}

.bank-details-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
}

.bank-details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.copy-bank-details {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.copy-bank-details:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.copy-bank-details i {
    font-size: 0.8rem;
}

.bank-details-header h4 {
    margin: 0;
    color: #28a745;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.bank-details-header h4 i {
    font-size: 0.9rem;
}



.bank-details {
    background: rgba(40, 167, 69, 0.05);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid rgba(40, 167, 69, 0.1);
    font-family: 'Courier New', monospace;
    white-space: pre-line;
    color: #333;
    font-size: 0.9rem;
    line-height: 1.6;
    margin: 0;
}

.bank-details strong {
    color: #28a745;
    font-weight: 600;
}

/* Compact Receipt Upload Form */
.receipt-upload-form {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.receipt-upload-form h3 {
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
    font-weight: 600;
}

.receipt-upload-form h3 i {
    color: #d4822a;
    font-size: 1rem;
}

/* Compact Upload Form */
.upload-form {
    max-width: 100%;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
    font-size: 0.9rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #d4822a;
    background: white;
    box-shadow: 0 0 0 2px rgba(212, 130, 42, 0.1);
}

/* Compact File Upload Area */
.file-upload-area {
    position: relative;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 25px 15px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background: #fafafa;
}

.file-upload-area:hover {
    border-color: #d4822a;
    background: rgba(212, 130, 42, 0.05);
}

.file-upload-area input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-upload-text i {
    font-size: 2rem;
    color: #d4822a;
    margin-bottom: 8px;
}

.file-upload-text p {
    margin: 0 0 3px 0;
    color: #333;
    font-weight: 500;
    font-size: 0.9rem;
}

.file-upload-text small {
    color: #666;
    font-size: 0.8rem;
}

.file-upload-area.dragover {
    border-color: #d4822a;
    background: rgba(212, 130, 42, 0.1);
}

/* Compact Upload Button */
.btn-large {
    width: 100%;
    padding: 12px 20px;
    font-size: 0.95rem;
    font-weight: 600;
    border-radius: 8px;
    background: linear-gradient(135deg, #d4822a 0%, #b8860b 100%);
    border: none;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 130, 42, 0.3);
}

.btn-large:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 130, 42, 0.4);
}

/* Compact Existing Receipt */
.existing-receipt {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.2);
    position: relative;
    overflow: hidden;
}

.existing-receipt::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
}

.existing-receipt h3 {
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
    font-weight: 600;
}

.existing-receipt h3 i {
    color: #28a745;
    font-size: 1rem;
}

/* Compact Receipt Info */
.receipt-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px 15px;
}

.receipt-item {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.receipt-item label {
    font-weight: 500;
    color: #666;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.receipt-item span {
    color: #333;
    font-size: 0.9rem;
    font-weight: 500;
}

.verification-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 600;
    font-size: 0.9rem;
}

.verification-status.verified {
    color: #28a745;
}

.verification-status.pending {
    color: #ffc107;
}

.receipt-link {
    color: #d4822a;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    font-size: 0.9rem;
    padding: 6px 12px;
    background: rgba(212, 130, 42, 0.1);
    border-radius: 6px;
    transition: all 0.3s ease;
    width: fit-content;
}

.receipt-link:hover {
    background: rgba(212, 130, 42, 0.2);
    text-decoration: none;
    transform: translateY(-1px);
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .payment-container {
        grid-template-columns: 1fr 1fr;
        max-width: 1000px;
    }

    .receipt-upload-form,
    .existing-receipt {
        grid-column: 1 / -1;
    }
}

@media (max-width: 968px) {
    .payment-container {
        grid-template-columns: 1fr;
        padding: 12px;
        gap: 15px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .summary-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .receipt-info {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

@media (max-width: 768px) {
    /* Ensure mobile scrolling works properly */
    body .dashboard {
        height: auto !important;
        overflow: visible !important;
    }

    .payment-container {
        padding: 10px;
        gap: 12px;
        margin-bottom: 20px;
    }

    .booking-summary-card,
    .payment-instructions,
    .receipt-upload-form,
    .existing-receipt {
        padding: 15px;
    }

    .booking-summary-card h3,
    .payment-instructions h3,
    .receipt-upload-form h3,
    .existing-receipt h3 {
        font-size: 1rem;
        margin-bottom: 12px;
    }

    .summary-item.total {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        padding: 10px 12px;
    }

    .instruction-step {
        gap: 10px;
    }

    .step-number {
        width: 24px;
        height: 24px;
        font-size: 0.8rem;
    }

    .step-content h4 {
        font-size: 0.95rem;
    }

    .step-content p {
        font-size: 0.85rem;
    }

    .bank-details {
        font-size: 0.8rem;
        padding: 10px;
    }

    .file-upload-area {
        padding: 20px 12px;
    }

    .file-upload-text i {
        font-size: 1.5rem;
    }
}
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .file-upload-area {
        padding: 30px 15px;
    }
    
    .file-upload-text i {
        font-size: 2.5rem;
    }
    
    .receipt-info {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .booking-summary-card,
    .payment-instructions,
    .receipt-upload-form,
    .existing-receipt {
        padding: 15px;
    }
    
    .file-upload-area {
        padding: 25px 10px;
    }
    
    .file-upload-text i {
        font-size: 2rem;
    }
    
    .btn-large {
        width: 100%;
    }
}
