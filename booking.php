<?php
require_once 'includes/auth.php';

// Require user to be logged in
requireLogin();

$auth = getAuth();
$user = $auth->getCurrentUser();
$db = getDB();

// Get dhana types
$dhanaTypes = $db->fetchAll("SELECT * FROM dhana_types WHERE is_active = 1 ORDER BY price DESC");

// Get pre-selected date from URL
$selectedDate = isset($_GET['date']) ? $_GET['date'] : '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_booking'])) {
    $dhanaTypeId = (int)$_POST['dhana_type_id'];
    $bookingDate = $_POST['booking_date'];
    $bookingTimeSlot = $_POST['booking_time_slot'];
    $specialRequests = trim($_POST['special_requests']);
    $travelSupport = isset($_POST['travel_support']) ? 1 : 0;
    $isAnnualEvent = isset($_POST['is_annual_event']) ? 1 : 0;
    $isMonk = isset($_POST['is_monk']) ? 1 : 0;

    // Validate inputs
    $errors = [];

    if (empty($dhanaTypeId)) {
        $errors[] = 'Please select a dhana type';
    }

    if (empty($bookingDate)) {
        $errors[] = 'Please select a booking date';
    } else {
        // Check if date is not in the past
        if ($bookingDate < date('Y-m-d')) {
            $errors[] = 'Booking date cannot be in the past';
        }

        // For annual events, allow booking up to 10 years
        $maxDate = $isAnnualEvent ?
            date('Y-m-d', strtotime('+10 years')) :
            date('Y-m-d', strtotime('+10 years'));
    }

    if (empty($errors)) {
        try {
            // Check for booking conflicts based on whole day logic
            $conflictingBookings = [];

            if ($bookingTimeSlot === 'whole_day') {
                // Whole day booking conflicts with ANY booking on the same date
                $conflictingBookings = $db->fetchAll(
                    "SELECT id, booking_time_slot, dhana_type_id FROM bookings
                     WHERE booking_date = ? AND status NOT IN ('cancelled')",
                    [$bookingDate]
                );
            } else {
                // Specific time slot conflicts with same time slot for same dhana type OR any whole day booking
                $conflictingBookings = $db->fetchAll(
                    "SELECT id, booking_time_slot, dhana_type_id FROM bookings
                     WHERE booking_date = ? AND status NOT IN ('cancelled')
                     AND (
                         (dhana_type_id = ? AND booking_time_slot = ?)
                         OR booking_time_slot = 'whole_day'
                     )",
                    [$bookingDate, $dhanaTypeId, $bookingTimeSlot]
                );
            }

            if (!empty($conflictingBookings)) {
                // Determine specific error message
                $hasWholeDayConflict = false;
                foreach ($conflictingBookings as $conflict) {
                    if ($conflict['booking_time_slot'] === 'whole_day') {
                        $hasWholeDayConflict = true;
                        break;
                    }
                }

                if ($bookingTimeSlot === 'whole_day') {
                    $errors[] = 'Cannot book whole day - there are existing bookings for this date';
                } elseif ($hasWholeDayConflict) {
                    $errors[] = 'This date is fully booked (whole day booking exists)';
                } else {
                    $errors[] = 'This dhana type and time slot is already booked for the selected date';
                }
            } else {
                // Get dhana type details for pricing
                $dhanaType = $db->fetchOne(
                    "SELECT * FROM dhana_types WHERE id = ? AND is_active = 1",
                    [$dhanaTypeId]
                );

                if (!$dhanaType) {
                    $errors[] = 'Invalid dhana type selected';
                } else {
                    // Create the main booking
                    $db->query(
                        "INSERT INTO bookings (user_id, dhana_type_id, booking_date, booking_time_slot, special_requests, travel_support, is_annual_event, is_monk, total_amount, status)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')",
                        [$user['id'], $dhanaTypeId, $bookingDate, $bookingTimeSlot, $specialRequests, $travelSupport, $isAnnualEvent, $isMonk, $dhanaType['price']]
                    );

                    $bookingId = $db->lastInsertId();

                    // If annual event, create bookings for the next 10 years
                    if ($isAnnualEvent) {
                        $currentYear = date('Y', strtotime($bookingDate));
                        $baseDate = new DateTime($bookingDate);

                        // Create annual booking record for tracking
                        $db->query(
                            "INSERT INTO annual_bookings (booking_id, year_start, year_end) VALUES (?, ?, ?)",
                            [$bookingId, $currentYear, $currentYear + 9] // 10 years total including the first year
                        );

                        // Create individual booking records for years 2-10 (year 1 is already created above)
                        for ($yearOffset = 1; $yearOffset <= 9; $yearOffset++) {
                            $nextYearDate = clone $baseDate;
                            $nextYearDate->modify("+{$yearOffset} year");

                            // Check if this date would conflict with existing bookings
                            $futureDate = $nextYearDate->format('Y-m-d');
                            $conflictCheck = $db->fetchAll(
                                "SELECT id FROM bookings
                                 WHERE booking_date = ? AND status NOT IN ('cancelled')
                                 AND ((dhana_type_id = ? AND booking_time_slot = ?) OR booking_time_slot = 'whole_day')",
                                [$futureDate, $dhanaTypeId, $bookingTimeSlot]
                            );

                            // Only create if no conflicts (annual bookings take precedence for future years)
                            if (empty($conflictCheck)) {
                                // Check if parent_booking_id column exists
                                try {
                                    $db->query(
                                        "INSERT INTO bookings (user_id, dhana_type_id, booking_date, booking_time_slot, special_requests, travel_support, is_annual_event, is_monk, total_amount, status, parent_booking_id)
                                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?)",
                                        [$user['id'], $dhanaTypeId, $futureDate, $bookingTimeSlot, $specialRequests, $travelSupport, $isAnnualEvent, $isMonk, $dhanaType['price'], $bookingId]
                                    );
                                } catch (Exception $e) {
                                    // Fallback if parent_booking_id column doesn't exist
                                    $db->query(
                                        "INSERT INTO bookings (user_id, dhana_type_id, booking_date, booking_time_slot, special_requests, travel_support, is_annual_event, is_monk, total_amount, status)
                                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')",
                                        [$user['id'], $dhanaTypeId, $futureDate, $bookingTimeSlot, $specialRequests, $travelSupport, $isAnnualEvent, $isMonk, $dhanaType['price']]
                                    );
                                }
                            }
                        }
                    }

                    // Redirect to payment page
                    header("Location: payment.php?booking_id=" . $bookingId);
                    exit;
                }
            }
        } catch (Exception $e) {
            error_log("Reservation error: " . $e->getMessage());
            $errors[] = 'An error occurred while processing your reservation. Please try again.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Dhana Reservation - Dhana Reservation System</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/booking-steps.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard">
        <!-- Header -->
        <div class="dashboard-header">
            <h1><i class="fas fa-plus-circle"></i> Create New Dhana Reservation</h1>
            <p>Complete your dhana reservation in simple steps</p>
            <a href="dashboard.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>

        <!-- Reservation Form -->
        <div class="booking-form-container">
            <form id="bookingForm" method="POST" class="booking-form">
                <?php if (!empty($errors)): ?>
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i>
                        <ul>
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <!-- Step 1: Select Dhana Type -->
                <div class="form-section">
                    <h3><i class="fas fa-hand-holding-heart"></i> Step 1: Select Dhana Type</h3>
                    
                    <div class="dhana-types-grid">
                        <?php foreach ($dhanaTypes as $type): ?>
                            <div class="dhana-type-card">
                                <input type="radio" 
                                       id="dhana_<?php echo $type['id']; ?>" 
                                       name="dhana_type_id" 
                                       value="<?php echo $type['id']; ?>"
                                       <?php echo ($type['price'] == 0) ? 'disabled' : ''; ?>
                                       onchange="updateTotalAmount()">
                                
                                <label for="dhana_<?php echo $type['id']; ?>" class="dhana-type-label <?php echo ($type['price'] == 0) ? 'disabled' : ''; ?>">
                                    <div class="dhana-type-header">
                                        <h4><?php echo htmlspecialchars($type['name']); ?></h4>
                                        <div class="dhana-type-price">
                                            <?php if ($type['price'] > 0): ?>
                                                Rs. <?php echo number_format($type['price']); ?>
                                            <?php else: ?>
                                                <span class="coming-soon">Coming Soon</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <?php if ($type['description']): ?>
                                        <p class="dhana-type-description">
                                            <?php echo htmlspecialchars($type['description']); ?>
                                        </p>
                                    <?php endif; ?>
                                    
                                    <?php if ($type['price'] == 0): ?>
                                        <div class="disabled-overlay">
                                            <i class="fas fa-clock"></i>
                                            Coming Soon
                                        </div>
                                    <?php endif; ?>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Step 2: Select Date -->
                <div class="form-section">
                    <h3><i class="fas fa-calendar-alt"></i> Step 2: Select Date</h3>
                    
                    <div class="date-selection">
                        <div class="form-group">
                            <label for="booking_date">Booking Date</label>
                            <input type="date" 
                                   id="booking_date" 
                                   name="booking_date" 
                                   value="<?php echo htmlspecialchars($selectedDate); ?>"
                                   min="<?php echo date('Y-m-d', strtotime('+1 day')); ?>"
                                   max="<?php echo date('Y-m-d', strtotime('+30 days')); ?>"
                                   required
                                   onchange="checkDateAvailability()">
                        </div>
                        
                        <div class="calendar-link">
                            <a href="calendar.php" class="btn btn-secondary">
                                <i class="fas fa-calendar-check"></i>
                                View Calendar
                            </a>
                        </div>
                    </div>
                    
                    <div id="dateAvailability" class="date-availability"></div>
                </div>

                <!-- Step 3: Additional Details -->
                <div class="form-section">
                    <h3><i class="fas fa-edit"></i> Step 3: Additional Details</h3>
                    
                    <div class="form-group">
                        <label for="special_requests">Special Requests (Optional)</label>
                        <textarea id="special_requests" 
                                  name="special_requests" 
                                  rows="4" 
                                  placeholder="Any special requirements or requests for your dhana booking..."><?php echo isset($_POST['special_requests']) ? htmlspecialchars($_POST['special_requests']) : ''; ?></textarea>
                    </div>
                </div>

                <!-- Booking Summary -->
                <div class="booking-summary">
                    <h3><i class="fas fa-receipt"></i> Booking Summary</h3>
                    
                    <div class="summary-row">
                        <span>Donor:</span>
                        <span><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></span>
                    </div>
                    
                    <div class="summary-row">
                        <span>Email:</span>
                        <span><?php echo htmlspecialchars($user['email']); ?></span>
                    </div>
                    
                    <div class="summary-row">
                        <span>Dhana Type:</span>
                        <span id="selectedDhanaType">Please select a dhana type</span>
                    </div>
                    
                    <div class="summary-row">
                        <span>Date:</span>
                        <span id="selectedDate">Please select a date</span>
                    </div>
                    
                    <div class="summary-row total">
                        <span>Total Amount:</span>
                        <span id="totalAmount">Rs. 0</span>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-large">
                        <i class="fas fa-arrow-right"></i>
                        Proceed to Payment
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        const dhanaTypes = <?php echo json_encode($dhanaTypes); ?>;
        const userId = <?php echo $user['id']; ?>;
    </script>
    <script src="assets/js/booking.js"></script>
</body>
</html>
