<?php
/**
 * Clean All Booking Records - Fresh Start Script
 * WARNING: This will permanently delete ALL booking data!
 * Use only for development/testing purposes.
 */

require_once 'config/database.php';

// Check if cleanup should be executed
$execute = isset($_POST['execute']) ? $_POST['execute'] : '';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clean Booking Records - Dhana System</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2rem;
        }
        
        .header .warning {
            margin-top: 10px;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .warning-box h3 {
            color: #856404;
            margin-top: 0;
        }
        
        .warning-box ul {
            color: #856404;
            margin-bottom: 0;
        }
        
        .danger-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .danger-box h3 {
            color: #721c24;
            margin-top: 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        
        .stat-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #dc3545;
        }
        
        .confirmation-form {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
        }
        

        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .back-link {
            text-align: center;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-exclamation-triangle"></i> Clean Booking Records</h1>
            <div class="warning">Permanent Data Deletion Tool</div>
        </div>
        
        <div class="content">
            <?php
            if ($execute === 'yes') {
                // Execute the cleanup
                try {
                    $db = getDB();
                    
                    // Start transaction
                    $db->query("START TRANSACTION");
                    
                    // Get counts before deletion
                    $bookingCount = $db->fetchOne("SELECT COUNT(*) as count FROM bookings")['count'];
                    $receiptCount = $db->fetchOne("SELECT COUNT(*) as count FROM payment_receipts")['count'];
                    $annualCount = $db->fetchOne("SELECT COUNT(*) as count FROM annual_bookings")['count'];
                    
                    // Delete in correct order (respecting foreign keys)
                    $db->query("DELETE FROM annual_bookings");
                    $db->query("DELETE FROM payment_receipts");
                    $db->query("DELETE FROM bookings");
                    
                    // Reset auto-increment counters
                    $db->query("ALTER TABLE bookings AUTO_INCREMENT = 1");
                    $db->query("ALTER TABLE payment_receipts AUTO_INCREMENT = 1");
                    $db->query("ALTER TABLE annual_bookings AUTO_INCREMENT = 1");
                    
                    // Commit transaction
                    $db->query("COMMIT");
                    
                    echo '<div class="success-message">';
                    echo '<h3><i class="fas fa-check-circle"></i> Cleanup Completed Successfully!</h3>';
                    echo '<p><strong>Records Deleted:</strong></p>';
                    echo '<ul>';
                    echo '<li>' . $bookingCount . ' booking records</li>';
                    echo '<li>' . $receiptCount . ' payment receipt records</li>';
                    echo '<li>' . $annualCount . ' annual booking records</li>';
                    echo '</ul>';
                    echo '<p>All booking data has been permanently removed and auto-increment counters have been reset.</p>';
                    echo '</div>';
                    
                } catch (Exception $e) {
                    $db->query("ROLLBACK");
                    echo '<div class="danger-box">';
                    echo '<h3>Error During Cleanup</h3>';
                    echo '<p>An error occurred: ' . htmlspecialchars($e->getMessage()) . '</p>';
                    echo '</div>';
                }
                
            } else {
                // Show confirmation form
                try {
                    $db = getDB();
                    
                    // Get current record counts
                    $bookingCount = $db->fetchOne("SELECT COUNT(*) as count FROM bookings")['count'];
                    $receiptCount = $db->fetchOne("SELECT COUNT(*) as count FROM payment_receipts")['count'];
                    $annualCount = $db->fetchOne("SELECT COUNT(*) as count FROM annual_bookings")['count'];
                    $userCount = $db->fetchOne("SELECT COUNT(*) as count FROM users")['count'];
                    
                    echo '<div class="danger-box">';
                    echo '<h3><i class="fas fa-skull-crossbones"></i> DANGER ZONE</h3>';
                    echo '<p><strong>This action will permanently delete ALL booking data from the database!</strong></p>';
                    echo '<p>This includes all reservations, payment receipts, and annual booking records.</p>';
                    echo '</div>';
                    
                    echo '<div class="warning-box">';
                    echo '<h3><i class="fas fa-info-circle"></i> What will be deleted:</h3>';
                    echo '<ul>';
                    echo '<li>All booking/reservation records</li>';
                    echo '<li>All payment receipt uploads and records</li>';
                    echo '<li>All annual booking configurations</li>';
                    echo '<li>Auto-increment counters will be reset</li>';
                    echo '</ul>';
                    echo '<p><strong>Note:</strong> User accounts and dhana types will NOT be deleted.</p>';
                    echo '</div>';
                    
                    echo '<div class="stats-grid">';
                    echo '<div class="stat-card">';
                    echo '<h4>Total Bookings</h4>';
                    echo '<div class="number">' . $bookingCount . '</div>';
                    echo '</div>';
                    echo '<div class="stat-card">';
                    echo '<h4>Payment Receipts</h4>';
                    echo '<div class="number">' . $receiptCount . '</div>';
                    echo '</div>';
                    echo '<div class="stat-card">';
                    echo '<h4>Annual Bookings</h4>';
                    echo '<div class="number">' . $annualCount . '</div>';
                    echo '</div>';
                    echo '<div class="stat-card">';
                    echo '<h4>User Accounts</h4>';
                    echo '<div class="number">' . $userCount . ' <small>(preserved)</small></div>';
                    echo '</div>';
                    echo '</div>';
                    
                    if ($bookingCount > 0 || $receiptCount > 0 || $annualCount > 0) {
                        echo '<form method="POST" class="confirmation-form">';
                        echo '<h3>Click the button below to clean all booking data:</h3>';
                        echo '<input type="hidden" name="execute" value="yes">';
                        echo '<button type="submit" class="btn btn-danger">';
                        echo '<i class="fas fa-trash-alt"></i> CLEAN ALL BOOKING DATA';
                        echo '</button>';
                        echo '</form>';
                    } else {
                        echo '<div class="success-message">';
                        echo '<h3>No Data to Clean</h3>';
                        echo '<p>The database is already clean - no booking records found.</p>';
                        echo '</div>';
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="danger-box">';
                    echo '<h3>Database Error</h3>';
                    echo '<p>Could not connect to database: ' . htmlspecialchars($e->getMessage()) . '</p>';
                    echo '</div>';
                }
            }
            ?>
            
            <div class="back-link">
                <a href="admin/index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Admin Panel
                </a>
            </div>
        </div>
    </div>
</body>
</html>
