<?php
require_once 'includes/auth.php';

// Require user to be logged in
requireLogin();

$auth = getAuth();
$user = $auth->getCurrentUser();

// Handle logout
if (isset($_POST['action']) && $_POST['action'] === 'logout') {
    $auth->logout();
    header('Location: index.php');
    exit;
}

// Get dhana types for pricing display
$db = getDB();
$dhanaTypes = $db->fetchAll("SELECT * FROM dhana_types WHERE is_active = 1 ORDER BY price DESC");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Dhana Reservation System</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard">
        <!-- Top Welcome Section -->
        <div class="dashboard-header">
            <div class="user-info">
                <p>Hello, <strong><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></strong></p>
                <p>Email: <?php echo htmlspecialchars($user['email']); ?></p>
            </div>
            <div class="welcome-content">
                <h1><i class="fas fa-lotus"></i> Welcome to Dhana Reservation</h1>
            </div>
            <div class="user-actions">
                <a href="settings.php" class="settings-btn">
                    <i class="fas fa-cog"></i> Settings
                </a>
                <form method="POST" class="logout-form">
                    <input type="hidden" name="action" value="logout">
                    <button type="submit" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                </form>
            </div>
        </div>

        <!-- Main Dashboard Content -->
        <div class="dashboard-content">

            <!-- Left Panel: How to Place Reservation -->
            <div class="dashboard-panel left-panel">
                <div class="panel-header">
                    <h2><i class="fas fa-list-ol"></i> How to Place Your Dhana Reservation</h2>
                </div>
                <div class="panel-content">
                    <p class="guide-intro">Follow these simple steps to complete your sacred dhana reservation</p>

                    <div class="steps-compact">
                        <div class="step-compact">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h4><i class="fas fa-calendar-check"></i> Check Availability</h4>
                                <p>Browse our calendar to find available dates for your preferred dhana offering.</p>
                            </div>
                        </div>

                        <div class="step-compact">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h4><i class="fas fa-hand-holding-heart"></i> Select Dhana Type</h4>
                                <p>Choose from our dhana offerings:</p>
                                <ul class="dhana-types-list">
                                    <?php foreach ($dhanaTypes as $type): ?>
                                        <li>
                                            <strong><?php echo htmlspecialchars($type['name']); ?></strong> -
                                            <?php if ($type['price'] > 0): ?>
                                                Rs. <?php echo number_format($type['price']); ?>
                                            <?php else: ?>
                                                Coming Soon
                                            <?php endif; ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>

                        <div class="step-compact">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h4><i class="fas fa-edit"></i> Fill Reservation Form</h4>
                                <p>Complete the reservation form with your details and special requests.</p>
                            </div>
                        </div>

                        <div class="step-compact">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h4><i class="fas fa-credit-card"></i> Make Payment</h4>
                                <p>Transfer the amount and upload payment receipt.</p>
                            </div>
                        </div>

                        <div class="step-compact">
                            <div class="step-number">5</div>
                            <div class="step-content">
                                <h4><i class="fas fa-check-circle"></i> Receive Confirmation</h4>
                                <p>Get confirmation email with reservation details.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Middle Panel: Action Buttons -->
            <div class="dashboard-panel middle-panel">
                <div class="panel-content">
                    <div class="action-buttons-vertical">
                        <a href="calendar.php" class="action-btn-large">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Check Available Dates</span>
                            <small>View calendar and select your preferred date</small>
                        </a>

                        <a href="booking-new.php" class="action-btn-large">
                            <i class="fas fa-plus-circle"></i>
                            <span>Create New Reservation</span>
                            <small>Step-by-step reservation process</small>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Right Panel: Recent Reservations -->
            <div class="dashboard-panel right-panel">
                <div class="panel-header">
                    <h2><i class="fas fa-history"></i> Your Recent Reservations</h2>
                    <a href="my-bookings.php" class="btn btn-secondary btn-small">
                        <i class="fas fa-list"></i> View All Reservations
                    </a>
                </div>
                <div class="panel-content">
                    <?php
                    // Get user's recent reservations including annual events
                    $recentBookings = $db->fetchAll(
                        "SELECT b.*, dt.name as dhana_type_name, dt.price,
                                ab.year_start, ab.year_end,
                                CASE
                                    WHEN b.is_annual_event = 1 AND b.parent_booking_id IS NULL THEN 'main_annual'
                                    WHEN b.is_annual_event = 1 AND b.parent_booking_id IS NOT NULL THEN 'annual_instance'
                                    ELSE 'regular'
                                END as booking_type
                         FROM bookings b
                         JOIN dhana_types dt ON b.dhana_type_id = dt.id
                         LEFT JOIN annual_bookings ab ON (b.id = ab.booking_id OR b.parent_booking_id = ab.booking_id)
                         WHERE b.user_id = ?
                         ORDER BY b.created_at DESC, b.booking_date ASC
                         LIMIT 10",
                        [$user['id']]
                    );
                    ?>

                    <?php if (empty($recentBookings)): ?>
                        <div class="no-bookings">
                            <i class="fas fa-calendar-times"></i>
                            <p>You haven't made any reservations yet.</p>
                            <p>Click "Create New Reservation" to get started!</p>
                        </div>
                    <?php else: ?>
                        <div class="bookings-list-compact">
                            <?php foreach ($recentBookings as $booking): ?>
                                <div class="booking-item-compact">
                                    <div class="booking-info">
                                        <h4>
                                            <?php echo htmlspecialchars($booking['dhana_type_name']); ?>
                                            <?php if ($booking['is_annual_event']): ?>
                                                <span class="annual-badge">
                                                    <i class="fas fa-calendar-check"></i> Annual
                                                </span>
                                            <?php endif; ?>
                                        </h4>
                                        <p><i class="fas fa-calendar"></i> <?php echo date('M j, Y', strtotime($booking['booking_date'])); ?></p>
                                        <p><i class="fas fa-money-bill"></i> Rs. <?php echo number_format($booking['total_amount']); ?></p>
                                        <?php if ($booking['is_annual_event'] && $booking['year_start'] && $booking['year_end']): ?>
                                            <p><i class="fas fa-repeat"></i> <?php echo $booking['year_start']; ?> - <?php echo $booking['year_end']; ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="booking-status">
                                        <span class="status-badge status-<?php echo $booking['status']; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $booking['status'])); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/dashboard.js"></script>
</body>
</html>
