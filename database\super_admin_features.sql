-- Super Admin Features Database Updates
-- Run this in phpMyAdmin to add super admin functionality

USE dhana_booking;

-- Create approval workflow table for tracking admin actions
CREATE TABLE IF NOT EXISTS admin_actions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL,
    action_type ENUM('booking_update', 'user_status_change', 'settings_update', 'dhana_type_update') NOT NULL,
    action_description TEXT NOT NULL,
    target_id INT NULL, -- ID of the affected record (booking_id, user_id, etc.)
    old_values JSON NULL, -- Store old values for rollback
    new_values JSON NULL, -- Store new values
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    approved_by INT NULL, -- Super admin who approved/rejected
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admin_users(id),
    FOREIG<PERSON> KEY (approved_by) REFERENCES admin_users(id)
);

-- Add role column to users table to track admin status
ALTER TABLE users 
ADD COLUMN role ENUM('donor', 'admin') DEFAULT 'donor' AFTER is_active;

-- Update existing admin_users to have super_admin role for the first admin
UPDATE admin_users SET role = 'super_admin' WHERE id = 1;

-- Create user role change log table
CREATE TABLE IF NOT EXISTS user_role_changes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    old_role ENUM('donor', 'admin') NOT NULL,
    new_role ENUM('donor', 'admin') NOT NULL,
    changed_by INT NOT NULL, -- Super admin who made the change
    reason TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (changed_by) REFERENCES admin_users(id)
);

-- Add admin_user_id column to users table to link admin accounts
ALTER TABLE users 
ADD COLUMN admin_user_id INT NULL AFTER role,
ADD FOREIGN KEY (admin_user_id) REFERENCES admin_users(id);

-- Create pending admin actions view for easy querying
CREATE OR REPLACE VIEW pending_admin_actions AS
SELECT 
    aa.*,
    au.username as admin_username,
    au.email as admin_email,
    sau.username as approver_username
FROM admin_actions aa
JOIN admin_users au ON aa.admin_id = au.id
LEFT JOIN admin_users sau ON aa.approved_by = sau.id
WHERE aa.status = 'pending'
ORDER BY aa.created_at ASC;
