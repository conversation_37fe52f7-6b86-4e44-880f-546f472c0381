-- Update database schema for new reservation features
-- Run this in phpMyAdmin after the initial setup

USE dhana_booking;

-- Add new columns to bookings table
ALTER TABLE bookings 
ADD COLUMN travel_support BOOLEAN DEFAULT FALSE AFTER special_requests,
ADD COLUMN is_annual_event BOOLEAN DEFAULT FALSE AFTER travel_support,
ADD COLUMN is_monk BOOLEAN DEFAULT FALSE AFTER is_annual_event,
ADD COLUMN booking_time_slot ENUM('morning', 'lunch', 'whole_day') DEFAULT 'whole_day' AFTER booking_date;

-- Update dhana_types table to include time slots
ALTER TABLE dhana_types 
ADD COLUMN time_slot ENUM('morning', 'lunch', 'whole_day', 'extra') DEFAULT 'whole_day' AFTER description;

-- Update existing dhana types with time slots
UPDATE dhana_types SET time_slot = 'whole_day' WHERE name = 'Whole Day';
UPDATE dhana_types SET time_slot = 'lunch' WHERE name = 'Lunch Dhana';
UPDATE dhana_types SET time_slot = 'morning' WHERE name = 'Morning Dhana';
UPDATE dhana_types SET time_slot = 'extra' WHERE name = 'Extra Item';

-- Create annual reservations table for recurring events
CREATE TABLE annual_bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    year_start INT NOT NULL,
    year_end INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE
);

-- Update the unique constraint to include time slot
ALTER TABLE bookings DROP INDEX unique_booking_date;
ALTER TABLE bookings ADD UNIQUE KEY unique_booking_date_slot (booking_date, dhana_type_id, booking_time_slot);
