-- Update Database Terminology
-- Replace 'dana' with 'dhana' and 'booking' with 'reservation' in database content
-- Run this script to update existing database content

USE dhana_booking;

-- Update dhana_types table - replace 'Dana' with 'Dhana' in names and descriptions
UPDATE dhana_types SET name = 'Morning Dhana' WHERE name = 'Morning Dana';
UPDATE dhana_types SET name = 'Lunch Dhana' WHERE name = 'Lunch Dana';
UPDATE dhana_types SET name = 'Evening Dhana' WHERE name = 'Evening Dana';

UPDATE dhana_types SET description = 'Morning dhana offering' WHERE description = 'Morning dana offering';
UPDATE dhana_types SET description = 'Lunch time dhana offering' WHERE description = 'Lunch time dana offering';
UPDATE dhana_types SET description = 'Evening dhana offering' WHERE description = 'Evening dana offering';
UPDATE dhana_types SET description = 'Complete day dhana offering' WHERE description = 'Complete day dana offering';

-- Update settings table - replace 'dana' with 'dhana' and 'booking' with 'reservation'
UPDATE settings SET setting_value = 'Dhana Reservation System' WHERE setting_key = 'site_name' AND setting_value = 'Dhana Booking System';
UPDATE settings SET setting_value = 'Dhana Reservation System' WHERE setting_key = 'site_name' AND setting_value = 'Dana Booking System';

UPDATE settings SET description = 'How many days in advance reservations can be made' WHERE setting_key = 'booking_advance_days';
UPDATE settings SET description = 'Maximum reservations allowed per day per dhana type' WHERE setting_key = 'max_bookings_per_day';

UPDATE settings SET setting_value = REPLACE(setting_value, 'dana', 'dhana') WHERE setting_value LIKE '%dana%';
UPDATE settings SET setting_value = REPLACE(setting_value, 'Dana', 'Dhana') WHERE setting_value LIKE '%Dana%';
UPDATE settings SET setting_value = REPLACE(setting_value, 'booking', 'reservation') WHERE setting_value LIKE '%booking%';
UPDATE settings SET setting_value = REPLACE(setting_value, 'Booking', 'Reservation') WHERE setting_value LIKE '%Booking%';

UPDATE settings SET description = REPLACE(description, 'dana', 'dhana') WHERE description LIKE '%dana%';
UPDATE settings SET description = REPLACE(description, 'Dana', 'Dhana') WHERE description LIKE '%Dana%';
UPDATE settings SET description = REPLACE(description, 'booking', 'reservation') WHERE description LIKE '%booking%';
UPDATE settings SET description = REPLACE(description, 'Booking', 'Reservation') WHERE description LIKE '%Booking%';

-- Update any other text fields that might contain 'dana' or 'booking' terminology
-- Check blocked_dates table for reason field
UPDATE blocked_dates SET reason = REPLACE(reason, 'dana', 'dhana') WHERE reason LIKE '%dana%';
UPDATE blocked_dates SET reason = REPLACE(reason, 'Dana', 'Dhana') WHERE reason LIKE '%Dana%';
UPDATE blocked_dates SET reason = REPLACE(reason, 'booking', 'reservation') WHERE reason LIKE '%booking%';
UPDATE blocked_dates SET reason = REPLACE(reason, 'Booking', 'Reservation') WHERE reason LIKE '%Booking%';

-- Update admin_actions table descriptions
UPDATE admin_actions SET action_description = REPLACE(action_description, 'dana', 'dhana') WHERE action_description LIKE '%dana%';
UPDATE admin_actions SET action_description = REPLACE(action_description, 'Dana', 'Dhana') WHERE action_description LIKE '%Dana%';
UPDATE admin_actions SET action_description = REPLACE(action_description, 'booking', 'reservation') WHERE action_description LIKE '%booking%';
UPDATE admin_actions SET action_description = REPLACE(action_description, 'Booking', 'Reservation') WHERE action_description LIKE '%Booking%';

-- Update any special_requests in bookings table that might contain these terms
UPDATE bookings SET special_requests = REPLACE(special_requests, 'dana', 'dhana') WHERE special_requests LIKE '%dana%';
UPDATE bookings SET special_requests = REPLACE(special_requests, 'Dana', 'Dhana') WHERE special_requests LIKE '%Dana%';

-- Show results
SELECT 'dhana_types table updated' as status;
SELECT name, description FROM dhana_types;

SELECT 'settings table updated' as status;
SELECT setting_key, setting_value, description FROM settings WHERE setting_value LIKE '%dhana%' OR description LIKE '%dhana%' OR setting_value LIKE '%reservation%' OR description LIKE '%reservation%';

SELECT 'Database terminology update completed successfully!' as final_status;
