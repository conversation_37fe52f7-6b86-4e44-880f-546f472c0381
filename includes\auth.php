<?php
/**
 * Authentication System for Dhana Booking
 */

require_once __DIR__ . '/../config/database.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * Register a new user
     */
    public function register($firstName, $lastName, $email, $contactNumber, $password) {
        try {
            // Validate input
            $errors = $this->validateRegistration($firstName, $lastName, $email, $contactNumber, $password);
            if (!empty($errors)) {
                return ['success' => false, 'errors' => $errors];
            }
            
            // Check if email already exists
            $existingUser = $this->db->fetchOne(
                "SELECT id FROM users WHERE email = ?", 
                [$email]
            );
            
            if ($existingUser) {
                return ['success' => false, 'errors' => ['Email already registered']];
            }
            
            // Hash password
            $passwordHash = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert new user
            $this->db->query(
                "INSERT INTO users (first_name, last_name, email, contact_number, password_hash) VALUES (?, ?, ?, ?, ?)",
                [$firstName, $lastName, $email, $contactNumber, $passwordHash]
            );
            
            $userId = $this->db->lastInsertId();
            
            // Auto-login after registration
            $this->createSession($userId, $firstName, $lastName, $email);
            
            return ['success' => true, 'message' => 'Registration successful'];
            
        } catch (Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['Registration failed. Please try again.']];
        }
    }
    
    /**
     * Login user
     */
    public function login($email, $password) {
        try {
            $user = $this->db->fetchOne(
                "SELECT id, first_name, last_name, email, password_hash, is_active FROM users WHERE email = ?",
                [$email]
            );
            
            if (!$user) {
                return ['success' => false, 'error' => 'Invalid email or password'];
            }
            
            if (!$user['is_active']) {
                return ['success' => false, 'error' => 'Account is deactivated'];
            }
            
            if (!password_verify($password, $user['password_hash'])) {
                return ['success' => false, 'error' => 'Invalid email or password'];
            }
            
            // Create session
            $this->createSession($user['id'], $user['first_name'], $user['last_name'], $user['email']);
            
            return ['success' => true, 'message' => 'Login successful'];
            
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Login failed. Please try again.'];
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        session_destroy();
        return ['success' => true, 'message' => 'Logged out successfully'];
    }
    
    /**
     * Check if user is logged in
     */
    public function isLoggedIn() {
        return isset($_SESSION['user_id']) && isset($_SESSION['user_email']);
    }
    
    /**
     * Get current user info
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'first_name' => $_SESSION['user_first_name'],
            'last_name' => $_SESSION['user_last_name'],
            'email' => $_SESSION['user_email']
        ];
    }
    
    /**
     * Create user session
     */
    private function createSession($userId, $firstName, $lastName, $email) {
        $_SESSION['user_id'] = $userId;
        $_SESSION['user_first_name'] = $firstName;
        $_SESSION['user_last_name'] = $lastName;
        $_SESSION['user_email'] = $email;
        $_SESSION['login_time'] = time();
    }
    
    /**
     * Validate registration data
     */
    private function validateRegistration($firstName, $lastName, $email, $contactNumber, $password) {
        $errors = [];
        
        if (empty($firstName) || strlen($firstName) < 2) {
            $errors[] = 'First name must be at least 2 characters';
        }
        
        if (empty($lastName) || strlen($lastName) < 2) {
            $errors[] = 'Last name must be at least 2 characters';
        }
        
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Valid email is required';
        }
        
        if (empty($contactNumber) || strlen($contactNumber) < 10) {
            $errors[] = 'Valid contact number is required';
        }
        
        if (empty($password) || strlen($password) < PASSWORD_MIN_LENGTH) {
            $errors[] = 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters';
        }
        
        return $errors;
    }
    
    /**
     * Check session timeout
     */
    public function checkSessionTimeout() {
        if ($this->isLoggedIn() && isset($_SESSION['login_time'])) {
            if (time() - $_SESSION['login_time'] > SESSION_TIMEOUT) {
                $this->logout();
                return false;
            }
        }
        return true;
    }
}

// Helper function to get auth instance
function getAuth() {
    return new Auth();
}

// Helper function to require login
function requireLogin() {
    $auth = getAuth();
    if (!$auth->isLoggedIn() || !$auth->checkSessionTimeout()) {
        header('Location: index.php');
        exit;
    }
}
?>
