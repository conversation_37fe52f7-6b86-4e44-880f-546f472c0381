<?php
require_once 'includes/auth.php';

// Require user to be logged in
requireLogin();

$auth = getAuth();
$user = $auth->getCurrentUser();
$db = getDB();

// Get reservation ID from URL
$bookingId = isset($_GET['booking_id']) ? (int)$_GET['booking_id'] : 0;

if (!$bookingId) {
    header('Location: dashboard.php');
    exit;
}

// Get reservation details
$booking = $db->fetchOne(
    "SELECT b.*, dt.name as dhana_type_name, dt.price, u.first_name, u.last_name, u.email, u.contact_number
     FROM bookings b 
     JOIN dhana_types dt ON b.dhana_type_id = dt.id 
     JOIN users u ON b.user_id = u.id 
     WHERE b.id = ? AND b.user_id = ?",
    [$bookingId, $user['id']]
);

if (!$booking) {
    header('Location: dashboard.php');
    exit;
}

// Get bank details from settings
$bankDetails = $db->fetchOne(
    "SELECT setting_value FROM settings WHERE setting_key = 'bank_details'"
);

// Handle receipt upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['receipt'])) {
    $errors = [];
    
    $paymentMethod = trim($_POST['payment_method']);
    $paymentReference = trim($_POST['payment_reference']);
    
    if (empty($paymentMethod)) {
        $errors[] = 'Please select a payment method';
    }
    
    if (empty($paymentReference)) {
        $errors[] = 'Please enter payment reference/transaction ID';
    }
    
    // Validate file upload
    $file = $_FILES['receipt'];
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errors[] = 'Please select a receipt file to upload';
    } else {
        $fileSize = $file['size'];
        $fileName = $file['name'];
        $fileTmpName = $file['tmp_name'];
        $fileType = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        
        // Check file size (5MB max)
        if ($fileSize > MAX_FILE_SIZE) {
            $errors[] = 'File size must be less than 5MB';
        }
        
        // Check file type
        if (!in_array($fileType, ALLOWED_FILE_TYPES)) {
            $errors[] = 'Only JPG, JPEG, PNG, and PDF files are allowed';
        }
    }
    
    if (empty($errors)) {
        try {
            // Generate unique filename
            $newFileName = 'receipt_' . $bookingId . '_' . time() . '.' . $fileType;
            $uploadPath = UPLOAD_DIR . $newFileName;
            
            // Move uploaded file
            if (move_uploaded_file($fileTmpName, $uploadPath)) {
                // Save receipt record
                $db->query(
                    "INSERT INTO payment_receipts (booking_id, receipt_filename, payment_method, payment_reference) 
                     VALUES (?, ?, ?, ?)",
                    [$bookingId, $newFileName, $paymentMethod, $paymentReference]
                );
                
                // Update reservation status
                $db->query(
                    "UPDATE bookings SET status = 'payment_pending' WHERE id = ?",
                    [$bookingId]
                );
                
                $successMessage = 'Receipt uploaded successfully! Your dhana reservation is now pending payment verification.';
                
                // Refresh reservation data
                $booking = $db->fetchOne(
                    "SELECT b.*, dt.name as dhana_type_name, dt.price, u.first_name, u.last_name, u.email, u.contact_number
                     FROM bookings b 
                     JOIN dhana_types dt ON b.dhana_type_id = dt.id 
                     JOIN users u ON b.user_id = u.id 
                     WHERE b.id = ? AND b.user_id = ?",
                    [$bookingId, $user['id']]
                );
            } else {
                $errors[] = 'Failed to upload receipt. Please try again.';
            }
        } catch (Exception $e) {
            error_log("Receipt upload error: " . $e->getMessage());
            $errors[] = 'An error occurred while uploading the receipt. Please try again.';
        }
    }
}

// Get existing receipt if any
$existingReceipt = $db->fetchOne(
    "SELECT * FROM payment_receipts WHERE booking_id = ? ORDER BY upload_date DESC LIMIT 1",
    [$bookingId]
);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - Dhana Reservation System</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/payment.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard">
        <!-- Header -->
        <div class="dashboard-header">
            <h1><i class="fas fa-credit-card"></i> Payment & Receipt Upload</h1>
            <p>Complete your dhana reservation by uploading payment receipt</p>
            <a href="dashboard.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>

        <!-- Three-Column Payment Container -->
        <div class="payment-container">
            <!-- Column 1: Dhana Reservation Summary -->
            <div class="booking-summary-card">
                <h3><i class="fas fa-receipt"></i> Dhana Reservation Summary</h3>
                
                <div class="summary-grid">
                    <div class="summary-item">
                        <label>Reservation ID:</label>
                        <span>#<?php echo str_pad($booking['id'], 6, '0', STR_PAD_LEFT); ?></span>
                    </div>
                    
                    <div class="summary-item">
                        <label>Donor:</label>
                        <span><?php echo htmlspecialchars($booking['first_name'] . ' ' . $booking['last_name']); ?></span>
                    </div>
                    
                    <div class="summary-item">
                        <label>Email:</label>
                        <span><?php echo htmlspecialchars($booking['email']); ?></span>
                    </div>
                    
                    <div class="summary-item">
                        <label>Contact:</label>
                        <span><?php echo htmlspecialchars($booking['contact_number']); ?></span>
                    </div>
                    
                    <div class="summary-item">
                        <label>Dhana Type:</label>
                        <span><?php echo htmlspecialchars($booking['dhana_type_name']); ?></span>
                    </div>
                    
                    <div class="summary-item">
                        <label>Date:</label>
                        <span><?php echo date('F j, Y', strtotime($booking['booking_date'])); ?></span>
                    </div>
                    
                    <div class="summary-item">
                        <label>Status:</label>
                        <span class="status-badge status-<?php echo $booking['status']; ?>">
                            <?php echo ucfirst(str_replace('_', ' ', $booking['status'])); ?>
                        </span>
                    </div>
                    
                    <div class="summary-item total">
                        <label>Total Amount:</label>
                        <span>Rs. <?php echo number_format($booking['total_amount']); ?></span>
                    </div>
                </div>
                
                <?php if ($booking['special_requests']): ?>
                    <div class="special-requests">
                        <label>Special Requests:</label>
                        <p><?php echo nl2br(htmlspecialchars($booking['special_requests'])); ?></p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Column 2: Payment Instructions -->
            <div class="payment-instructions">
                <h3><i class="fas fa-university"></i> Payment Instructions</h3>
                
                <div class="instruction-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>Transfer the Amount</h4>
                        <p>Transfer <strong>Rs. <?php echo number_format($booking['total_amount']); ?></strong> to our bank account using the details below:</p>

                        <div class="bank-details-card">
                            <div class="bank-details-header">
                                <h4><i class="fas fa-university"></i> Bank Details</h4>
                                <button class="copy-bank-details" onclick="copyBankDetails()">
                                    <i class="fas fa-copy"></i> Copy Details
                                </button>
                            </div>
                            <div class="bank-details" id="bankDetailsText">
                                <?php if ($bankDetails): ?>
                                    <?php echo nl2br(htmlspecialchars($bankDetails['setting_value'])); ?>
                                <?php else: ?>
                                    <p>Bank details will be provided via email.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="instruction-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>Upload Receipt</h4>
                        <p>After making the payment, upload your receipt or transaction screenshot using the form below.</p>
                    </div>
                </div>
                
                <div class="instruction-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>Wait for Confirmation</h4>
                        <p>We'll verify your payment and send you a confirmation email within 24 hours.</p>
                    </div>
                </div>
            </div>

            <!-- Column 3: Receipt Upload Form -->
            <?php if ($booking['status'] === 'pending' || $booking['status'] === 'payment_pending'): ?>
                <div class="receipt-upload-form">
                    <h3><i class="fas fa-upload"></i> Upload Payment Receipt</h3>
                    
                    <?php if (isset($successMessage)): ?>
                        <div class="success-message">
                            <i class="fas fa-check-circle"></i>
                            <?php echo htmlspecialchars($successMessage); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($errors)): ?>
                        <div class="error-message">
                            <i class="fas fa-exclamation-circle"></i>
                            <ul>
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" enctype="multipart/form-data" class="upload-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="payment_method">Payment Method</label>
                                <select id="payment_method" name="payment_method" required>
                                    <option value="">Select payment method</option>
                                    <option value="bank_transfer">Bank Transfer</option>
                                    <option value="online_banking">Online Banking</option>
                                    <option value="mobile_banking">Mobile Banking</option>
                                    <option value="cash_deposit">Cash Deposit</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="payment_reference">Transaction ID / Reference</label>
                                <input type="text" id="payment_reference" name="payment_reference" 
                                       placeholder="Enter transaction ID or reference number" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="receipt">Receipt File</label>
                            <div class="file-upload-area">
                                <input type="file" id="receipt" name="receipt" accept=".jpg,.jpeg,.png,.pdf" required>
                                <div class="file-upload-text">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>Click to select receipt file or drag and drop</p>
                                    <small>Supported formats: JPG, PNG, PDF (Max 5MB)</small>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-large">
                            <i class="fas fa-upload"></i>
                            Upload Receipt
                        </button>
                    </form>
                </div>
            <?php endif; ?>

            <!-- Column 3: Existing Receipt (if no upload form) -->
            <?php if ($existingReceipt): ?>
                <div class="existing-receipt">
                    <h3><i class="fas fa-file-invoice"></i> Uploaded Receipt</h3>
                    
                    <div class="receipt-info">
                        <div class="receipt-item">
                            <label>Upload Date:</label>
                            <span><?php echo date('F j, Y g:i A', strtotime($existingReceipt['upload_date'])); ?></span>
                        </div>
                        
                        <div class="receipt-item">
                            <label>Payment Method:</label>
                            <span><?php echo ucfirst(str_replace('_', ' ', $existingReceipt['payment_method'])); ?></span>
                        </div>
                        
                        <div class="receipt-item">
                            <label>Reference:</label>
                            <span><?php echo htmlspecialchars($existingReceipt['payment_reference']); ?></span>
                        </div>
                        
                        <div class="receipt-item">
                            <label>Status:</label>
                            <span class="verification-status <?php echo $existingReceipt['verified'] ? 'verified' : 'pending'; ?>">
                                <?php if ($existingReceipt['verified']): ?>
                                    <i class="fas fa-check-circle"></i> Verified
                                <?php else: ?>
                                    <i class="fas fa-clock"></i> Pending Verification
                                <?php endif; ?>
                            </span>
                        </div>
                        
                        <div class="receipt-item">
                            <label>File:</label>
                            <a href="<?php echo UPLOAD_DIR . $existingReceipt['receipt_filename']; ?>" 
                               target="_blank" class="receipt-link">
                                <i class="fas fa-external-link-alt"></i>
                                View Receipt
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div> <!-- Close payment-container -->
    </div> <!-- Close dashboard -->

    <script src="assets/js/payment.js"></script>
    <script>
        // Copy bank details function
        function copyBankDetails() {
            const bankDetailsText = document.getElementById('bankDetailsText').innerText;

            if (navigator.clipboard && window.isSecureContext) {
                // Use modern clipboard API
                navigator.clipboard.writeText(bankDetailsText).then(() => {
                    showCopySuccess();
                }).catch(() => {
                    fallbackCopy(bankDetailsText);
                });
            } else {
                // Fallback for older browsers
                fallbackCopy(bankDetailsText);
            }
        }

        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showCopySuccess();
            } catch (err) {
                console.error('Failed to copy text: ', err);
                alert('Failed to copy bank details. Please copy manually.');
            }

            document.body.removeChild(textArea);
        }

        function showCopySuccess() {
            const button = document.querySelector('.copy-bank-details');
            const originalText = button.innerHTML;

            button.innerHTML = '<i class="fas fa-check"></i> Copied!';
            button.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';

            setTimeout(() => {
                button.innerHTML = originalText;
                button.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
            }, 2000);
        }
    </script>
</body>
</html>
