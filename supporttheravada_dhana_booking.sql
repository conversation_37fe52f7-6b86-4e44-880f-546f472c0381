-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Sep 01, 2025 at 04:31 AM
-- Server version: 10.6.23-MariaDB
-- PHP Version: 8.3.23

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `supporttheravada_dhana_booking`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin_actions`
--

CREATE TABLE `admin_actions` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `action_type` enum('booking_update','user_status_change','settings_update','dhana_type_update') NOT NULL,
  `action_description` text NOT NULL,
  `target_id` int(11) DEFAULT NULL,
  `old_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`old_values`)),
  `new_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`new_values`)),
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `approved_by` int(11) DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_actions`
--

INSERT INTO `admin_actions` (`id`, `admin_id`, `action_type`, `action_description`, `target_id`, `old_values`, `new_values`, `status`, `approved_by`, `approved_at`, `created_at`) VALUES
(1, 2, 'booking_update', 'Change dhana reservation #12 status from \'pending\' to \'confirmed\'', 12, '{\"status\":\"pending\"}', '{\"status\":\"confirmed\"}', 'approved', 1, '2025-07-20 17:55:17', '2025-07-20 17:54:15'),
(2, 2, 'booking_update', 'Change dhana reservation #35 status from \'payment_pending\' to \'confirmed\'', 35, '{\"status\":\"payment_pending\"}', '{\"status\":\"confirmed\"}', 'approved', 1, '2025-08-03 15:43:39', '2025-08-03 15:39:19'),
(3, 2, 'booking_update', 'Change dhana reservation #23 status from \'payment_pending\' to \'confirmed\'', 23, '{\"status\":\"payment_pending\"}', '{\"status\":\"confirmed\"}', 'approved', 1, '2025-08-03 15:43:43', '2025-08-03 15:39:24'),
(4, 2, 'booking_update', 'Change dhana reservation #23 status from \'payment_pending\' to \'cancelled\'', 23, '{\"status\":\"payment_pending\"}', '{\"status\":\"cancelled\"}', 'approved', 1, '2025-08-03 15:43:58', '2025-08-03 15:39:34');

-- --------------------------------------------------------

--
-- Table structure for table `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `password_hash` varchar(255) NOT NULL,
  `role` enum('admin','super_admin') DEFAULT 'admin',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_active` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_users`
--

INSERT INTO `admin_users` (`id`, `username`, `email`, `phone`, `password_hash`, `role`, `created_at`, `is_active`) VALUES
(1, '<EMAIL>', '<EMAIL>', NULL, '$2y$10$N1pw/20.lmyyfpXLgxuIN.uMktQJ3Xkz.SIxOcxjbAYsMRNcgUOJS', 'super_admin', '2025-07-14 12:00:43', 1),
(2, '<EMAIL>', '<EMAIL>', NULL, '$2y$10$xrjZxwgP4AivkOVsrmmMDOx0oULxVfXVX503L3OBRlYuM72LfgRVq', 'admin', '2025-07-16 06:37:15', 1),
(5, '<EMAIL>', '<EMAIL>', NULL, '$2y$10$oug4DrdcP5fFoqU2U5Rg1eRsGtwV.mSuVqevoYOkmHa3Xcb70Tyd6', 'admin', '2025-07-16 15:02:01', 1),
(6, '<EMAIL>', '<EMAIL>', NULL, '$2y$10$F9QJt8dDBQPckktiY5Z9qOlYTbFbUBCxiNJhqmqiEttK7OXF7PoDu', 'admin', '2025-07-31 02:41:38', 1),
(8, '<EMAIL>', '<EMAIL>', NULL, '$2y$10$6ohOoEl8odAVP.dp8.p/C.4QPajVI/Ir2h1liwSYA8ZIkB2EWyIJS', 'admin', '2025-08-03 15:34:46', 1),
(9, '<EMAIL>', '<EMAIL>', NULL, '$2y$10$NThnk2NNP5UD5n4Y6i13suJKXGH3qIF9JY5XesYYWqdsVUbWla7cy', 'admin', '2025-08-03 15:37:02', 1),
(10, '<EMAIL>', '<EMAIL>', NULL, '$2y$10$nXeKXp.4I28njBeG5mBx8Or1zM8lUx7pc5Tx.qt5tmgZvwzK5fRS.', 'admin', '2025-08-03 15:38:01', 1);

-- --------------------------------------------------------

--
-- Table structure for table `annual_bookings`
--

CREATE TABLE `annual_bookings` (
  `id` int(11) NOT NULL,
  `booking_id` int(11) NOT NULL,
  `year_start` int(11) NOT NULL,
  `year_end` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `annual_bookings`
--

INSERT INTO `annual_bookings` (`id`, `booking_id`, `year_start`, `year_end`, `created_at`) VALUES
(1, 2, 2025, 2034, '2025-07-19 11:19:58'),
(2, 12, 2025, 2034, '2025-07-20 17:52:43'),
(3, 24, 2025, 2034, '2025-08-03 03:46:50'),
(4, 35, 2025, 2034, '2025-08-03 14:15:12'),
(5, 46, 2025, 2034, '2025-08-03 15:00:22');

-- --------------------------------------------------------

--
-- Table structure for table `blocked_dates`
--

CREATE TABLE `blocked_dates` (
  `id` int(11) NOT NULL,
  `blocked_date` date NOT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `bookings`
--

CREATE TABLE `bookings` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `dhana_type_id` int(11) NOT NULL,
  `booking_date` date NOT NULL,
  `booking_time_slot` enum('morning','lunch','whole_day') DEFAULT 'whole_day',
  `booking_time` time DEFAULT NULL,
  `special_requests` text DEFAULT NULL,
  `travel_support` tinyint(1) DEFAULT 0,
  `is_annual_event` tinyint(1) DEFAULT 0,
  `is_monk` tinyint(1) DEFAULT 0,
  `parent_booking_id` int(11) DEFAULT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `status` enum('pending','payment_pending','confirmed','completed','cancelled') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `bookings`
--

INSERT INTO `bookings` (`id`, `user_id`, `dhana_type_id`, `booking_date`, `booking_time_slot`, `booking_time`, `special_requests`, `travel_support`, `is_annual_event`, `is_monk`, `parent_booking_id`, `total_amount`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, 2, '2025-07-31', 'lunch', NULL, '', 0, 0, 0, NULL, 100000.00, 'pending', '2025-07-19 10:55:18', '2025-07-19 10:55:18'),
(2, 1, 2, '2025-07-25', 'lunch', NULL, '', 1, 1, 1, NULL, 80000.00, 'pending', '2025-07-19 11:19:58', '2025-07-24 12:45:56'),
(3, 1, 3, '2026-07-25', 'morning', NULL, '', 1, 1, 1, 2, 80000.00, 'pending', '2025-07-19 11:19:58', '2025-07-19 11:19:58'),
(4, 1, 3, '2027-07-25', 'morning', NULL, '', 1, 1, 1, 2, 80000.00, 'pending', '2025-07-19 11:19:58', '2025-07-19 11:19:58'),
(5, 1, 3, '2028-07-25', 'morning', NULL, '', 1, 1, 1, 2, 80000.00, 'pending', '2025-07-19 11:19:58', '2025-07-19 11:19:58'),
(6, 1, 3, '2029-07-25', 'morning', NULL, '', 1, 1, 1, 2, 80000.00, 'pending', '2025-07-19 11:19:58', '2025-07-19 11:19:58'),
(7, 1, 3, '2030-07-25', 'morning', NULL, '', 1, 1, 1, 2, 80000.00, 'pending', '2025-07-19 11:19:58', '2025-07-19 11:19:58'),
(8, 1, 3, '2031-07-25', 'morning', NULL, '', 1, 1, 1, 2, 80000.00, 'pending', '2025-07-19 11:19:58', '2025-07-19 11:19:58'),
(9, 1, 3, '2032-07-25', 'morning', NULL, '', 1, 1, 1, 2, 80000.00, 'pending', '2025-07-19 11:19:58', '2025-07-19 11:19:58'),
(10, 1, 3, '2033-07-25', 'morning', NULL, '', 1, 1, 1, 2, 80000.00, 'pending', '2025-07-19 11:19:58', '2025-07-19 11:19:58'),
(11, 1, 3, '2034-07-25', 'morning', NULL, '', 1, 1, 1, 2, 80000.00, 'pending', '2025-07-19 11:19:58', '2025-07-19 11:19:58'),
(12, 2, 1, '2025-07-28', 'whole_day', NULL, '', 1, 1, 0, NULL, 180000.00, 'confirmed', '2025-07-20 17:52:43', '2025-07-20 17:55:17'),
(13, 2, 1, '2026-07-28', 'whole_day', NULL, '', 1, 1, 0, 12, 180000.00, 'pending', '2025-07-20 17:52:43', '2025-07-20 17:52:43'),
(14, 2, 1, '2027-07-28', 'whole_day', NULL, '', 1, 1, 0, 12, 180000.00, 'pending', '2025-07-20 17:52:43', '2025-07-20 17:52:43'),
(15, 2, 1, '2028-07-28', 'whole_day', NULL, '', 1, 1, 0, 12, 180000.00, 'pending', '2025-07-20 17:52:43', '2025-07-20 17:52:43'),
(16, 2, 1, '2029-07-28', 'whole_day', NULL, '', 1, 1, 0, 12, 180000.00, 'pending', '2025-07-20 17:52:43', '2025-07-20 17:52:43'),
(17, 2, 1, '2030-07-28', 'whole_day', NULL, '', 1, 1, 0, 12, 180000.00, 'pending', '2025-07-20 17:52:43', '2025-07-20 17:52:43'),
(18, 2, 1, '2031-07-28', 'whole_day', NULL, '', 1, 1, 0, 12, 180000.00, 'pending', '2025-07-20 17:52:43', '2025-07-20 17:52:43'),
(19, 2, 1, '2032-07-28', 'whole_day', NULL, '', 1, 1, 0, 12, 180000.00, 'pending', '2025-07-20 17:52:43', '2025-07-20 17:52:43'),
(20, 2, 1, '2033-07-28', 'whole_day', NULL, '', 1, 1, 0, 12, 180000.00, 'pending', '2025-07-20 17:52:43', '2025-07-20 17:52:43'),
(21, 2, 1, '2034-07-28', 'whole_day', NULL, '', 1, 1, 0, 12, 180000.00, 'pending', '2025-07-20 17:52:43', '2025-07-20 17:52:43'),
(22, 1, 1, '2025-08-18', 'whole_day', NULL, '', 1, 0, 0, NULL, 180000.00, 'pending', '2025-07-29 01:25:24', '2025-07-29 01:25:24'),
(23, 4, 3, '2025-08-17', 'morning', NULL, '', 1, 0, 0, NULL, 80000.00, 'cancelled', '2025-07-29 01:35:33', '2025-08-03 15:43:58'),
(24, 1, 1, '2025-08-26', 'whole_day', NULL, 'For testing.', 1, 1, 1, NULL, 180000.00, 'confirmed', '2025-08-03 03:46:50', '2025-08-03 04:06:46'),
(25, 1, 1, '2026-08-26', 'whole_day', NULL, 'For testing.', 1, 1, 1, 24, 180000.00, 'pending', '2025-08-03 03:46:50', '2025-08-03 03:46:50'),
(26, 1, 1, '2027-08-26', 'whole_day', NULL, 'For testing.', 1, 1, 1, 24, 180000.00, 'pending', '2025-08-03 03:46:50', '2025-08-03 03:46:50'),
(27, 1, 1, '2028-08-26', 'whole_day', NULL, 'For testing.', 1, 1, 1, 24, 180000.00, 'pending', '2025-08-03 03:46:50', '2025-08-03 03:46:50'),
(28, 1, 1, '2029-08-26', 'whole_day', NULL, 'For testing.', 1, 1, 1, 24, 180000.00, 'pending', '2025-08-03 03:46:50', '2025-08-03 03:46:50'),
(29, 1, 1, '2030-08-26', 'whole_day', NULL, 'For testing.', 1, 1, 1, 24, 180000.00, 'pending', '2025-08-03 03:46:50', '2025-08-03 03:46:50'),
(30, 1, 1, '2031-08-26', 'whole_day', NULL, 'For testing.', 1, 1, 1, 24, 180000.00, 'pending', '2025-08-03 03:46:50', '2025-08-03 03:46:50'),
(31, 1, 1, '2032-08-26', 'whole_day', NULL, 'For testing.', 1, 1, 1, 24, 180000.00, 'pending', '2025-08-03 03:46:50', '2025-08-03 03:46:50'),
(32, 1, 1, '2033-08-26', 'whole_day', NULL, 'For testing.', 1, 1, 1, 24, 180000.00, 'pending', '2025-08-03 03:46:50', '2025-08-03 03:46:50'),
(33, 1, 1, '2034-08-26', 'whole_day', NULL, 'For testing.', 1, 1, 1, 24, 180000.00, 'pending', '2025-08-03 03:46:50', '2025-08-03 03:46:50'),
(34, 5, 3, '2025-08-27', 'morning', NULL, 'monk\'s test', 0, 0, 1, NULL, 80000.00, 'cancelled', '2025-08-03 03:58:23', '2025-08-03 04:06:30'),
(35, 7, 2, '2025-08-13', 'lunch', NULL, '', 1, 1, 0, NULL, 100000.00, 'confirmed', '2025-08-03 14:15:12', '2025-08-03 15:43:39'),
(36, 7, 2, '2026-08-13', 'lunch', NULL, '', 1, 1, 0, 35, 100000.00, 'pending', '2025-08-03 14:15:12', '2025-08-03 14:15:12'),
(37, 7, 2, '2027-08-13', 'lunch', NULL, '', 1, 1, 0, 35, 100000.00, 'pending', '2025-08-03 14:15:12', '2025-08-03 14:15:12'),
(38, 7, 2, '2028-08-13', 'lunch', NULL, '', 1, 1, 0, 35, 100000.00, 'pending', '2025-08-03 14:15:12', '2025-08-03 14:15:12'),
(39, 7, 2, '2029-08-13', 'lunch', NULL, '', 1, 1, 0, 35, 100000.00, 'pending', '2025-08-03 14:15:12', '2025-08-03 14:15:12'),
(40, 7, 2, '2030-08-13', 'lunch', NULL, '', 1, 1, 0, 35, 100000.00, 'pending', '2025-08-03 14:15:12', '2025-08-03 14:15:12'),
(41, 7, 2, '2031-08-13', 'lunch', NULL, '', 1, 1, 0, 35, 100000.00, 'pending', '2025-08-03 14:15:12', '2025-08-03 14:15:12'),
(42, 7, 2, '2032-08-13', 'lunch', NULL, '', 1, 1, 0, 35, 100000.00, 'pending', '2025-08-03 14:15:12', '2025-08-03 14:15:12'),
(43, 7, 2, '2033-08-13', 'lunch', NULL, '', 1, 1, 0, 35, 100000.00, 'pending', '2025-08-03 14:15:12', '2025-08-03 14:15:12'),
(44, 7, 2, '2034-08-13', 'lunch', NULL, '', 1, 1, 0, 35, 100000.00, 'pending', '2025-08-03 14:15:12', '2025-08-03 14:15:12'),
(45, 8, 3, '2025-08-20', 'morning', NULL, '', 0, 0, 0, NULL, 80000.00, 'pending', '2025-08-03 14:25:48', '2025-08-03 14:25:48'),
(46, 9, 1, '2025-08-25', 'whole_day', NULL, '', 1, 1, 1, NULL, 180000.00, 'pending', '2025-08-03 15:00:22', '2025-08-03 15:00:22'),
(47, 9, 1, '2026-08-25', 'whole_day', NULL, '', 1, 1, 1, 46, 180000.00, 'pending', '2025-08-03 15:00:22', '2025-08-03 15:00:22'),
(48, 9, 1, '2027-08-25', 'whole_day', NULL, '', 1, 1, 1, 46, 180000.00, 'pending', '2025-08-03 15:00:22', '2025-08-03 15:00:22'),
(49, 9, 1, '2028-08-25', 'whole_day', NULL, '', 1, 1, 1, 46, 180000.00, 'pending', '2025-08-03 15:00:22', '2025-08-03 15:00:22'),
(50, 9, 1, '2029-08-25', 'whole_day', NULL, '', 1, 1, 1, 46, 180000.00, 'pending', '2025-08-03 15:00:22', '2025-08-03 15:00:22'),
(51, 9, 1, '2030-08-25', 'whole_day', NULL, '', 1, 1, 1, 46, 180000.00, 'pending', '2025-08-03 15:00:22', '2025-08-03 15:00:22'),
(52, 9, 1, '2031-08-25', 'whole_day', NULL, '', 1, 1, 1, 46, 180000.00, 'pending', '2025-08-03 15:00:22', '2025-08-03 15:00:22'),
(53, 9, 1, '2032-08-25', 'whole_day', NULL, '', 1, 1, 1, 46, 180000.00, 'pending', '2025-08-03 15:00:22', '2025-08-03 15:00:22'),
(54, 9, 1, '2033-08-25', 'whole_day', NULL, '', 1, 1, 1, 46, 180000.00, 'pending', '2025-08-03 15:00:22', '2025-08-03 15:00:22'),
(55, 9, 1, '2034-08-25', 'whole_day', NULL, '', 1, 1, 1, 46, 180000.00, 'pending', '2025-08-03 15:00:22', '2025-08-03 15:00:22'),
(56, 1, 1, '2025-08-30', 'whole_day', NULL, '', 1, 0, 0, NULL, 180000.00, 'pending', '2025-08-24 06:20:45', '2025-08-24 06:20:45');

-- --------------------------------------------------------

--
-- Table structure for table `dhana_types`
--

CREATE TABLE `dhana_types` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `time_slot` enum('morning','lunch','whole_day','extra') DEFAULT 'whole_day',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `dhana_types`
--

INSERT INTO `dhana_types` (`id`, `name`, `price`, `description`, `time_slot`, `is_active`, `created_at`) VALUES
(1, 'Whole Day', 180000.00, 'Complete day dhana offering', 'whole_day', 1, '2025-07-14 12:00:43'),
(2, 'Lunch Dana', 100000.00, 'Lunch time dhana offering', 'lunch', 1, '2025-07-14 12:00:43'),
(3, 'Morning Dana', 80000.00, 'Morning dhana offering', 'morning', 1, '2025-07-14 12:00:43'),
(4, 'Extra Item', 0.00, 'Additional items - Coming Soon', 'extra', 1, '2025-07-14 12:00:43');

-- --------------------------------------------------------

--
-- Table structure for table `payment_receipts`
--

CREATE TABLE `payment_receipts` (
  `id` int(11) NOT NULL,
  `booking_id` int(11) NOT NULL,
  `receipt_filename` varchar(255) DEFAULT NULL,
  `upload_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `payment_method` varchar(50) DEFAULT NULL,
  `payment_reference` varchar(100) DEFAULT NULL,
  `verified` tinyint(1) DEFAULT 0,
  `verified_by` int(11) DEFAULT NULL,
  `verified_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `payment_receipts`
--

INSERT INTO `payment_receipts` (`id`, `booking_id`, `receipt_filename`, `upload_date`, `payment_method`, `payment_reference`, `verified`, `verified_by`, `verified_at`) VALUES
(1, 23, 'receipt_23_1753752984.jpg', '2025-07-29 01:36:24', 'cash_deposit', 'lj', 0, NULL, NULL),
(2, 24, 'receipt_24_1754192941.jpeg', '2025-08-03 03:49:01', 'mobile_banking', 'ID_1234', 1, 1, '2025-08-03 04:06:46'),
(3, 24, 'receipt_24_1754193009.jpg', '2025-08-03 03:50:09', 'cash_deposit', 'ID_1234', 1, 1, '2025-08-03 04:06:46'),
(4, 35, 'receipt_35_1754230578.jpg', '2025-08-03 14:16:18', 'cash_deposit', 'tgt', 0, NULL, NULL);

-- --------------------------------------------------------

--
-- Stand-in structure for view `pending_admin_actions`
-- (See below for the actual view)
--
CREATE TABLE `pending_admin_actions` (
`id` int(11)
,`admin_id` int(11)
,`action_type` enum('booking_update','user_status_change','settings_update','dhana_type_update')
,`action_description` text
,`target_id` int(11)
,`old_values` longtext
,`new_values` longtext
,`status` enum('pending','approved','rejected')
,`approved_by` int(11)
,`approved_at` timestamp
,`created_at` timestamp
,`admin_username` varchar(50)
,`admin_email` varchar(100)
,`approver_username` varchar(50)
);

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `description`, `updated_at`) VALUES
(1, 'site_name', 'Dhana Booking System', 'Website name', '2025-07-14 12:00:43'),
(2, 'contact_email', '<EMAIL>', 'Contact email for inquiries', '2025-07-14 12:00:43'),
(3, 'bank_details', 'Bank: ABC Bank\nAccount: **********\nName: Dhana Organization', 'Bank details for payments', '2025-07-14 12:00:43'),
(4, 'booking_advance_days', '30', 'How many days in advance bookings can be made', '2025-07-14 12:00:43'),
(5, 'max_bookings_per_day', '1', 'Maximum bookings allowed per day per dhana type', '2025-07-14 12:00:43');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `contact_number` varchar(20) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_active` tinyint(1) DEFAULT 1,
  `role` enum('donor','admin') DEFAULT 'donor',
  `admin_user_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `first_name`, `last_name`, `email`, `contact_number`, `password_hash`, `created_at`, `updated_at`, `is_active`, `role`, `admin_user_id`) VALUES
(1, 'Dinu', 'Sri', '<EMAIL>', '**********', '$2y$10$jyWeCP9OQdQGIQy62dAzX.bP4Xl1KcNbAGESj6ctCfiffgne39Nm2', '2025-07-14 12:03:00', '2025-07-16 15:02:01', 1, 'admin', NULL),
(2, 'ab', 'ab', '<EMAIL>', '**********', '$2y$10$xrjZxwgP4AivkOVsrmmMDOx0oULxVfXVX503L3OBRlYuM72LfgRVq', '2025-07-14 12:19:26', '2025-07-30 23:47:45', 1, 'admin', 2),
(3, 'test', 'account', '<EMAIL>', '**********', '$2y$10$EXMexlRrGOA5kKxq6hnAB.HOP7LmplMyuQyQHRgupTfnFCv/5m7Qy', '2025-07-15 05:30:39', '2025-07-20 17:55:33', 1, 'donor', NULL),
(4, 'Hemal', 'Li', '<EMAIL>', '**********', '$2y$10$S.JtQUlry1IydwsXmRi1duBE//j5Mtnc82IXfcQsWb1hxZHJ/YDFi', '2025-07-29 01:33:00', '2025-07-31 02:41:38', 1, 'admin', NULL),
(5, 'test', 'name', '<EMAIL>', '+***********', '$2y$10$AJRzKsktC7SR3/uq/TGmD.mxXxBKDHonOZb.rycQETULt85Uz6SG.', '2025-08-03 03:55:00', '2025-08-03 03:55:00', 1, 'donor', NULL),
(6, 'Nidula', 'Dharmapriya ', '<EMAIL>', '**********', '$2y$10$WfP0Kk3ZYL5PIOBQC8vqVO2NOyI0SWEfLc7LvqjEwfZek0b6Ea5Yq', '2025-08-03 05:01:33', '2025-08-03 15:34:46', 1, 'admin', NULL),
(7, 'ccc', 'ccb', '<EMAIL>', '*************', '$2y$10$JKMvA2xUh45bqlmjNQ44F.KDXxb7jx9SaqiCrWSfV.4MnQHS3IwiS', '2025-08-03 14:13:23', '2025-08-03 15:38:01', 1, 'admin', NULL),
(8, 'no', 'NO', '<EMAIL>', '**********', '$2y$10$PFdGkYhZvmnw6qHuwPGXW.o9HI3vAvI51Ux7dgoatPFZ6ZdXa2dlK', '2025-08-03 14:20:36', '2025-08-03 15:34:55', 1, 'donor', NULL),
(9, 'Madu', 'Shan', '<EMAIL>', '**********', '$2y$10$/JFQtlFSR6ANjQTa2aBlRO7vWNKXGEEinFi/PgJzZouqg.RBTq7De', '2025-08-03 14:37:20', '2025-08-03 15:37:02', 1, 'admin', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_role_changes`
--

CREATE TABLE `user_role_changes` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `old_role` enum('donor','admin') NOT NULL,
  `new_role` enum('donor','admin') NOT NULL,
  `changed_by` int(11) NOT NULL,
  `reason` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_role_changes`
--

INSERT INTO `user_role_changes` (`id`, `user_id`, `old_role`, `new_role`, `changed_by`, `reason`, `created_at`) VALUES
(1, 2, 'donor', 'admin', 1, NULL, '2025-07-16 06:37:15');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_actions`
--
ALTER TABLE `admin_actions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `admin_id` (`admin_id`),
  ADD KEY `approved_by` (`approved_by`);

--
-- Indexes for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `annual_bookings`
--
ALTER TABLE `annual_bookings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `booking_id` (`booking_id`);

--
-- Indexes for table `blocked_dates`
--
ALTER TABLE `blocked_dates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `bookings`
--
ALTER TABLE `bookings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_booking_date_slot` (`booking_date`,`dhana_type_id`,`booking_time_slot`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `dhana_type_id` (`dhana_type_id`),
  ADD KEY `parent_booking_id` (`parent_booking_id`);

--
-- Indexes for table `dhana_types`
--
ALTER TABLE `dhana_types`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `payment_receipts`
--
ALTER TABLE `payment_receipts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `booking_id` (`booking_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `admin_user_id` (`admin_user_id`);

--
-- Indexes for table `user_role_changes`
--
ALTER TABLE `user_role_changes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `changed_by` (`changed_by`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_actions`
--
ALTER TABLE `admin_actions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `annual_bookings`
--
ALTER TABLE `annual_bookings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `blocked_dates`
--
ALTER TABLE `blocked_dates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `bookings`
--
ALTER TABLE `bookings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=57;

--
-- AUTO_INCREMENT for table `dhana_types`
--
ALTER TABLE `dhana_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `payment_receipts`
--
ALTER TABLE `payment_receipts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `user_role_changes`
--
ALTER TABLE `user_role_changes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

-- --------------------------------------------------------

--
-- Structure for view `pending_admin_actions`
--
DROP TABLE IF EXISTS `pending_admin_actions`;

CREATE ALGORITHM=UNDEFINED DEFINER=`supporttheravada`@`localhost` SQL SECURITY DEFINER VIEW `pending_admin_actions`  AS SELECT `aa`.`id` AS `id`, `aa`.`admin_id` AS `admin_id`, `aa`.`action_type` AS `action_type`, `aa`.`action_description` AS `action_description`, `aa`.`target_id` AS `target_id`, `aa`.`old_values` AS `old_values`, `aa`.`new_values` AS `new_values`, `aa`.`status` AS `status`, `aa`.`approved_by` AS `approved_by`, `aa`.`approved_at` AS `approved_at`, `aa`.`created_at` AS `created_at`, `au`.`username` AS `admin_username`, `au`.`email` AS `admin_email`, `sau`.`username` AS `approver_username` FROM ((`admin_actions` `aa` join `admin_users` `au` on(`aa`.`admin_id` = `au`.`id`)) left join `admin_users` `sau` on(`aa`.`approved_by` = `sau`.`id`)) WHERE `aa`.`status` = 'pending' ORDER BY `aa`.`created_at` ASC ;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admin_actions`
--
ALTER TABLE `admin_actions`
  ADD CONSTRAINT `admin_actions_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admin_users` (`id`),
  ADD CONSTRAINT `admin_actions_ibfk_2` FOREIGN KEY (`approved_by`) REFERENCES `admin_users` (`id`);

--
-- Constraints for table `annual_bookings`
--
ALTER TABLE `annual_bookings`
  ADD CONSTRAINT `annual_bookings_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `blocked_dates`
--
ALTER TABLE `blocked_dates`
  ADD CONSTRAINT `blocked_dates_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admin_users` (`id`);

--
-- Constraints for table `bookings`
--
ALTER TABLE `bookings`
  ADD CONSTRAINT `bookings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `bookings_ibfk_2` FOREIGN KEY (`dhana_type_id`) REFERENCES `dhana_types` (`id`),
  ADD CONSTRAINT `bookings_ibfk_3` FOREIGN KEY (`parent_booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `payment_receipts`
--
ALTER TABLE `payment_receipts`
  ADD CONSTRAINT `payment_receipts_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`admin_user_id`) REFERENCES `admin_users` (`id`);

--
-- Constraints for table `user_role_changes`
--
ALTER TABLE `user_role_changes`
  ADD CONSTRAINT `user_role_changes_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `user_role_changes_ibfk_2` FOREIGN KEY (`changed_by`) REFERENCES `admin_users` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
